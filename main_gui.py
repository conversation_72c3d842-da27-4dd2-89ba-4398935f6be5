import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import pandas as pd
import os
import glob
from difflib import SequenceMatcher
from typing import Dict, List, Tuple, Optional
import threading
import datetime
import json
import openpyxl
from openpyxl.styles import Font
import re
import concurrent.futures
from pypinyin import lazy_pinyin, Style
import time
import platform
import sys

# 注释掉试用期相关常量
# TRIAL_DAYS = 7
# LICENSE_FILE = "license_info.dat"

# Windows 7兼容性处理
def check_windows_compatibility():
    """检查Windows版本兼容性"""
    try:
        win_version = platform.win32_ver()[0]
        if win_version in ['7', 'Vista', 'XP']:
            return False, win_version
        return True, win_version
    except:
        return True, "Unknown"

# 如果是Windows 7，显示兼容性提示
is_compatible, win_ver = check_windows_compatibility()
if not is_compatible:
    print(f"检测到Windows {win_ver}，程序将以兼容模式运行")

class LoadingDialog:
    """加载提示对话框"""
    def __init__(self, parent, title="加载中", message="正在处理，请稍候..."):
        self.parent = parent
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("300x120")
        self.dialog.resizable(False, False)

        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.center_window()

        # 创建界面
        self.create_widgets(message)

        # 禁用关闭按钮
        self.dialog.protocol("WM_DELETE_WINDOW", lambda: None)

    def center_window(self):
        """将窗口居中显示"""
        self.dialog.update_idletasks()

        # 获取父窗口位置和大小
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        # 计算居中位置
        dialog_width = self.dialog.winfo_reqwidth()
        dialog_height = self.dialog.winfo_reqheight()

        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2

        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

    def create_widgets(self, message):
        """创建界面元素"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill='both', expand=True)

        # 消息标签
        message_label = ttk.Label(main_frame, text=message, font=('SimHei', 12))
        message_label.pack(pady=(0, 15))

        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate', length=200)
        self.progress.pack(pady=(0, 10))
        self.progress.start(10)  # 开始动画

    def close(self):
        """关闭对话框"""
        self.progress.stop()
        self.dialog.grab_release()
        self.dialog.destroy()

# 注释掉试用期检验函数
# def check_trial():
#     today = datetime.date.today()
#     if not os.path.exists(LICENSE_FILE):
#         # 首次运行，写入日期
#         with open(LICENSE_FILE, "w") as f:
#             json.dump({"first_run": today.isoformat()}, f)
#         return True
#     else:
#         with open(LICENSE_FILE, "r") as f:
#             data = json.load(f)
#         first_run = datetime.date.fromisoformat(data["first_run"])
#         days_used = (today - first_run).days
#         if days_used < TRIAL_DAYS:
#             return True
#         else:
#             return False

class MedicalRecordsGUI:
    def __init__(self):
        # 在现有代码前添加
        is_compatible, win_ver = check_windows_compatibility()
        if not is_compatible:
            # Windows 7兼容模式
            self.compatibility_mode = True
            self.win_version = win_ver
        else:
            self.compatibility_mode = False
            self.win_version = win_ver

        # 注释掉试用期检验
        # if not check_trial():
        #     messagebox.showwarning("试用期到期", "试用期已结束，请联系作者获取正式授权！")
        #     exit()

        self.root = tk.Tk()
        self.root.title("河南省漯河市弘济医院医疗诊疗信息管理系统")

        # 注释掉分辨率判断和窗口自适应缩放功能
        # self.init_scaling_system()

        # 使用固定窗口大小，不进行分辨率自适应
        # screen_width = self.root.winfo_screenwidth()
        # screen_height = self.root.winfo_screenheight()

        # 计算最佳窗口尺寸
        # window_width, window_height = self.calculate_optimal_window_size(screen_width, screen_height)

        # 使用固定窗口大小
        window_width = 1200
        window_height = 700

        # 居中显示
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.resizable(True, True)

        # 设置固定最小窗口尺寸
        min_width = 1200  # 固定最小宽度
        min_height = 700  # 固定最小高度
        self.root.minsize(min_width, min_height)

        # 注释掉窗口大小变化事件绑定
        # self.root.bind('<Configure>', self.on_window_configure)

        # 设置固定缩放比例
        self.scale_factor = 1.0
        self.is_small_screen = False
        self.is_very_small_screen = False
        
        # 初始化数据
        self.manager = None
        self.excel_folder_path = ""
        self.current_search_results = {}  # 存储当前搜索结果的详细数据
        self.condition_var = tk.StringVar()  # 添加这行，避免搜索时报错
        self.copied_case_data = None  # 存储复制的病例数据，用于右键粘贴
        
        # 预定义医生列表
        self.doctors_list = [
            "贾长鹰",  "肖云红"
        ]
        
        # 设置样式
        self.setup_styles()
        
        # 创建菜单
        self.create_menu()

        # 创建界面
        self.create_widgets()

        # 初始化状态
        self.update_status("请选择Excel文件夹并加载数据")

    # 注释掉分辨率判断和缩放系统初始化
    # def init_scaling_system(self):
    #     """初始化界面缩放系统"""
    #     # 获取屏幕信息
    #     screen_width = self.root.winfo_screenwidth()
    #     screen_height = self.root.winfo_screenheight()

    #     print(f"检测到屏幕分辨率: {screen_width}x{screen_height}")

    #     # 基准分辨率（设计时的参考分辨率）
    #     self.base_width = 1366
    #     self.base_height = 768

    #     # 计算缩放比例 - 使用更严格的等比例缩放
    #     width_scale = screen_width / self.base_width
    #     height_scale = screen_height / self.base_height

    #     # 使用较小的缩放比例以确保界面完整显示
    #     self.scale_factor = min(width_scale, height_scale)

    #     # 检查屏幕类型
    #     self.is_small_screen = screen_width < 1366 or screen_height < 768
    #     self.is_very_small_screen = screen_width <= 1024 or screen_height <= 600

    #     # 对于小屏幕，确保界面能完整显示
    #     if self.is_very_small_screen:
    #         # 极小屏幕：确保最小可用空间
    #         min_scale = min(screen_width / 1000, screen_height / 600)
    #         self.scale_factor = min(self.scale_factor, min_scale)
    #         print("检测到极小屏幕，启用紧凑模式")
    #     elif self.is_small_screen:
    #         # 小屏幕：适度压缩
    #         min_scale = min(screen_width / 1200, screen_height / 700)
    #         self.scale_factor = min(self.scale_factor, min_scale)
    #         print("检测到小屏幕，启用压缩模式")

    #     # 限制缩放范围
    #     self.scale_factor = max(0.3, min(self.scale_factor, 2.0))

    #     # 存储原始窗口大小，用于动态缩放
    #     self.original_window_width = 1200
    #     self.original_window_height = 700

    #     # 初始化窗口大小跟踪变量
    #     self.last_window_width = None
    #     self.last_window_height = None
    #     self.last_window_size = None

    #     print(f"最终缩放比例: {self.scale_factor:.2f}")

    #     # 绑定窗口大小变化事件
    #     self.root.bind('<Configure>', self.on_window_resize)

    #     # 如果是小屏幕，显示分辨率建议
    #     if self.is_small_screen:
    #         self.show_resolution_suggestion(screen_width, screen_height)

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="视图", menu=view_menu)

        # 注释掉界面缩放子菜单
        # scale_menu = tk.Menu(view_menu, tearoff=0)
        # view_menu.add_cascade(label="界面缩放", menu=scale_menu)

        # scale_menu.add_command(label="自动缩放", command=lambda: self.set_manual_scale(None))
        # scale_menu.add_separator()
        # scale_menu.add_command(label="50%", command=lambda: self.set_manual_scale(0.5))
        # scale_menu.add_command(label="75%", command=lambda: self.set_manual_scale(0.75))
        # scale_menu.add_command(label="100%", command=lambda: self.set_manual_scale(1.0))
        # scale_menu.add_command(label="125%", command=lambda: self.set_manual_scale(1.25))
        # scale_menu.add_command(label="150%", command=lambda: self.set_manual_scale(1.5))

        # 帮助菜单（注释掉分辨率建议）
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        # help_menu.add_command(label="分辨率建议", command=self.show_resolution_help)

    # 注释掉缩放相关方法
    # def set_manual_scale(self, scale_factor):
    #     """设置手动缩放比例"""
    #     if scale_factor is None:
    #         # 重新计算自动缩放
    #         self.init_scaling_system()
    #     else:
    #         self.scale_factor = scale_factor
    #         print(f"手动设置缩放比例: {self.scale_factor:.2f}")

    #     # 重新创建界面
    #     messagebox.showinfo("缩放设置", f"缩放比例已设置为 {self.scale_factor:.0%}，请重启软件以应用新设置。")

    # def show_resolution_help(self):
    #     """显示分辨率帮助信息"""
    #     screen_width = self.root.winfo_screenwidth()
    #     screen_height = self.root.winfo_screenheight()

    #     help_text = f"""当前屏幕信息：
    # 分辨率: {screen_width}x{screen_height}
    # 当前缩放: {self.scale_factor:.0%}

    # 推荐设置：
    # • 最佳分辨率: 1366x768 或更高
    # • 如果屏幕较小，可以：
    #   1. 调整系统显示设置中的分辨率
    #   2. 调整系统显示设置中的缩放比例
    #   3. 使用菜单中的"界面缩放"功能

    # 小屏幕优化：
    # • 14寸屏幕建议使用75%缩放
    # • 13寸屏幕建议使用50-75%缩放
    # • 极小屏幕会自动启用标签页布局"""

    #     messagebox.showinfo("分辨率帮助", help_text)

    # def show_resolution_suggestion(self, screen_width, screen_height):
    #     """显示分辨率建议"""
    #     if screen_width < 1024 or screen_height < 600:
    #         # 对于极小屏幕，建议调整分辨率
    #         suggestion = (
    #             "检测到您的屏幕分辨率较低，可能影响软件显示效果。\n\n"
    #             f"当前分辨率: {screen_width}x{screen_height}\n"
    #             "建议分辨率: 至少1366x768\n\n"
    #             "您可以:\n"
    #             "1. 调整系统显示设置中的分辨率\n"
    #             "2. 调整系统显示设置中的缩放比例\n"
    #             "3. 使用更大的显示器\n\n"
    #             "软件已自动调整界面比例，但某些元素可能仍显示不完整。"
    #         )
    #         messagebox.showinfo("屏幕分辨率建议", suggestion)

    # 注释掉分辨率相关的窗口大小计算和事件处理方法
    # def calculate_optimal_window_size(self, screen_width, screen_height):
    #     """计算最佳窗口尺寸"""
    #     # 基础窗口尺寸
    #     if self.is_very_small_screen:
    #         # 极小屏幕使用更小的基础尺寸
    #         base_window_width = 1000
    #         base_window_height = 600
    #     else:
    #         base_window_width = 1200
    #         base_window_height = 700

    #     # 应用缩放
    #     scaled_width = int(base_window_width * self.scale_factor)
    #     scaled_height = int(base_window_height * self.scale_factor)

    #     # 确保不超过屏幕尺寸的95%
    #     max_width = int(screen_width * 0.95)
    #     max_height = int(screen_height * 0.95)

    #     window_width = min(scaled_width, max_width)
    #     window_height = min(scaled_height, max_height)

    #     return window_width, window_height

    # def on_window_resize(self, event):
    #     """窗口大小变化时的处理"""
    #     # 只处理主窗口的大小变化
    #     if event.widget != self.root:
    #         return

    #     # 获取当前窗口大小
    #     current_width = self.root.winfo_width()
    #     current_height = self.root.winfo_height()

    #     # 避免初始化时的错误调用
    #     if current_width <= 1 or current_height <= 1:
    #         return

    #     # 如果窗口大小有显著变化，重新计算缩放比例
    #     if self.last_window_width is not None and self.last_window_height is not None:
    #         if self.last_window_width > 0 and self.last_window_height > 0:
    #             width_change = abs(current_width - self.last_window_width) / self.last_window_width
    #             height_change = abs(current_height - self.last_window_height) / self.last_window_height

    #             # 如果变化超过10%，重新计算缩放
    #             if width_change > 0.1 or height_change > 0.1:
    #                 self.update_dynamic_scaling(current_width, current_height)

    #     self.last_window_width = current_width
    #     self.last_window_height = current_height

    # def update_dynamic_scaling(self, window_width, window_height):
    #     """根据窗口大小动态更新缩放比例"""
    #     # 计算基于当前窗口大小的缩放比例
    #     width_scale = window_width / self.original_window_width
    #     height_scale = window_height / self.original_window_height

    #     # 使用较小的缩放比例
    #     new_scale = min(width_scale, height_scale)

    #     # 限制缩放范围
    #     new_scale = max(0.3, min(new_scale, 2.0))

    #     # 如果缩放比例变化显著，更新界面
    #     if abs(new_scale - self.scale_factor) > 0.1:
    #         self.scale_factor = new_scale
    #         print(f"动态调整缩放比例: {self.scale_factor:.2f}")
    #         # 这里可以添加重新布局的逻辑，但为了避免频繁重绘，暂时只更新缩放因子

    # def on_window_configure(self, event):
    #     """窗口大小变化时的处理"""
    #     # 只处理主窗口的配置变化
    #     if event.widget != self.root:
    #         return

    #     current_size = (self.root.winfo_width(), self.root.winfo_height())

    #     # 如果窗口大小发生显著变化，重新计算缩放
    #     if self.last_window_size is None or \
    #        abs(current_size[0] - self.last_window_size[0]) > 50 or \
    #        abs(current_size[1] - self.last_window_size[1]) > 50:

    #         self.last_window_size = current_size
    #         # 可以在这里添加动态调整逻辑

    def scale_value(self, value):
        """固定返回原始数值，不进行缩放"""
        return int(value)

    def scale_font_size(self, base_size):
        """固定返回原始字体大小，不进行缩放"""
        return base_size

    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')

        # 使用缩放系统计算字体大小
        base_font_size = self.scale_font_size(12)
        title_font_size = self.scale_font_size(16)
        tree_font_size = self.scale_font_size(10)
        header_font_size = self.scale_font_size(12)

        # 定义全局字体
        self.default_font = ('SimHei', base_font_size)
        style.configure('.', font=self.default_font)
        style.configure('Title.TLabel', font=('SimHei', title_font_size, 'bold'))
        style.configure('Header.TLabel', font=('SimHei', header_font_size, 'bold'))
        style.configure('Status.TLabel', foreground='blue')
        style.configure('Treeview', font=('SimHei', tree_font_size))
        style.configure('Treeview.Heading', font=('SimHei', tree_font_size, 'bold'))

        # 设置控件的缩放样式
        style.configure('TButton', padding=(self.scale_value(6), self.scale_value(3)))
        style.configure('TEntry', padding=(self.scale_value(3), self.scale_value(2)))
        style.configure('TLabelFrame', padding=self.scale_value(10))

        # 设置Treeview行高
        style.configure('Treeview', rowheight=self.scale_value(20))
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', padx=self.scale_value(5), pady=self.scale_value(2))

        title_label = ttk.Label(title_frame, text="弘济医院医疗诊疗信息管理系统", style='Title.TLabel')
        title_label.pack()

        # 创建主要区域
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True)

        # 强制使用左右分栏布局
        self.create_paned_layout(main_frame)

        self.create_status_section(self.root)

    def create_paned_layout(self, parent):
        """创建可拖拽调整的多面板布局"""
        # 主水平分割窗口：左侧添加新病例 | 右侧所有其他功能
        main_paned = ttk.PanedWindow(parent, orient='horizontal')
        main_paned.pack(fill='both', expand=True)

        # 设置分割条宽度为最小值，减少空白
        try:
            main_paned.configure(sashwidth=1, sashrelief='flat', sashpad=0)
        except:
            pass

        # 保存面板引用用于调试
        self.main_paned = main_paned

        # 左侧区域 - 使用普通Frame作为面板，确保与右侧紧密连接
        left_container = ttk.Frame(main_paned)
        left_container.columnconfigure(0, weight=1)
        left_container.rowconfigure(0, weight=1)

        # 右侧垂直分割窗口：文件管理 | 查找相似病例 | 搜索结果
        right_paned = ttk.PanedWindow(main_paned, orient='vertical')

        # 设置右侧垂直分割条宽度
        try:
            right_paned.configure(sashwidth=1, sashrelief='flat', sashpad=0)
        except:
            pass

        # 添加到主分割窗口，确保紧密连接
        main_paned.add(left_container, weight=35)
        main_paned.add(right_paned, weight=65)

        # 在左侧容器中创建LabelFrame
        left_frame = ttk.LabelFrame(left_container, text="添加新病例", padding=self.scale_value(5))
        left_frame.pack(fill='both', expand=True)

        # 保存面板引用用于调试
        self.left_container = left_container
        self.right_paned = right_paned
        self.left_frame = left_frame

        # 绑定面板拖动事件
        main_paned.bind('<ButtonRelease-1>', self.on_paned_drag)
        right_paned.bind('<ButtonRelease-1>', self.on_paned_drag)

        # 创建右侧的三个独立面板
        file_frame = ttk.Frame(right_paned)
        file_frame.columnconfigure(0, weight=1)
        file_frame.rowconfigure(0, weight=1)

        search_frame = ttk.Frame(right_paned)
        search_frame.columnconfigure(0, weight=1)
        search_frame.rowconfigure(0, weight=1)

        results_frame = ttk.Frame(right_paned)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # 添加到右侧垂直分割窗口，确保紧密连接
        right_paned.add(file_frame, weight=20)
        right_paned.add(search_frame, weight=25)
        right_paned.add(results_frame, weight=55)

        # 存储框架引用，用于动态调整
        self.left_container = left_container
        self.left_frame = left_frame
        self.file_frame = file_frame
        self.search_frame = search_frame
        self.results_frame = results_frame

        # 创建各个功能区域
        self.create_add_record_content(left_frame)  # 直接创建内容，不需要额外的LabelFrame
        self.create_file_section(file_frame)
        self.create_search_section(search_frame)
        self.create_results_section(results_frame)

        # 延迟打印初始窗口信息
        self.root.after(500, self.print_initial_window_info)

    def print_initial_window_info(self):
        """打印初始窗口比例和大小信息"""
        try:
            print("\n" + "="*60)
            print("【初始窗口参数】")
            print("="*60)

            # 主窗口信息
            root_width = self.root.winfo_width()
            root_height = self.root.winfo_height()
            print(f"主窗口大小: {root_width} x {root_height}")

            # 主面板信息
            if hasattr(self, 'main_paned'):
                main_width = self.main_paned.winfo_width()
                main_height = self.main_paned.winfo_height()
                print(f"主面板大小: {main_width} x {main_height}")

                # 获取分割位置
                try:
                    sash_coord = self.main_paned.sash_coord(0)
                    left_width = sash_coord[0]
                    right_width = main_width - left_width
                except:
                    # 如果获取分割位置失败，使用权重估算
                    left_width = int(main_width * 0.35)  # 根据weight=35估算
                    right_width = main_width - left_width

                print(f"左侧面板宽度: {left_width} ({left_width/main_width*100:.1f}%)")
                print(f"右侧面板宽度: {right_width} ({right_width/main_width*100:.1f}%)")
                print(f"左右比例: {left_width}:{right_width}")

            # 右侧垂直面板信息
            if hasattr(self, 'right_paned'):
                right_height = self.right_paned.winfo_height()
                print(f"右侧垂直面板高度: {right_height}")

                # 获取垂直分割位置
                try:
                    sash_count = len(self.right_paned.panes())
                    if sash_count > 1:
                        for i in range(sash_count - 1):
                            try:
                                sash_y = self.right_paned.sash_coord(i)[1]
                                print(f"垂直分割线 {i+1} 位置: y={sash_y}")
                            except:
                                print(f"无法获取垂直分割线 {i+1} 位置")
                except Exception as e:
                    print(f"获取垂直分割信息失败: {e}")

            print("="*60)

        except Exception as e:
            print(f"打印窗口信息时出错: {e}")

    def on_paned_drag(self, event):
        """面板拖动事件处理"""
        try:
            print("\n" + "-"*40)
            print("【面板拖动后参数变化】")
            print("-"*40)

            # 主窗口信息
            root_width = self.root.winfo_width()
            root_height = self.root.winfo_height()
            print(f"主窗口大小: {root_width} x {root_height}")

            # 主面板信息
            if hasattr(self, 'main_paned'):
                main_width = self.main_paned.winfo_width()
                main_height = self.main_paned.winfo_height()
                print(f"主面板大小: {main_width} x {main_height}")

                # 获取分割位置
                try:
                    sash_coord = self.main_paned.sash_coord(0)
                    left_width = sash_coord[0]
                    right_width = main_width - left_width

                    print(f"左侧面板宽度: {left_width} ({left_width/main_width*100:.1f}%)")
                    print(f"右侧面板宽度: {right_width} ({right_width/main_width*100:.1f}%)")
                    print(f"左右比例: {left_width}:{right_width}")
                except Exception as e:
                    # 如果获取分割位置失败，使用权重估算
                    left_width = int(main_width * 0.35)  # 根据weight=35估算
                    right_width = main_width - left_width
                    print(f"左侧面板宽度(估算): {left_width} ({left_width/main_width*100:.1f}%)")
                    print(f"右侧面板宽度(估算): {right_width} ({right_width/main_width*100:.1f}%)")
                    print(f"左右比例(估算): {left_width}:{right_width}")
                    print(f"获取主面板分割信息失败，使用估算值: {e}")

            # 右侧垂直面板信息
            if hasattr(self, 'right_paned'):
                try:
                    right_height = self.right_paned.winfo_height()
                    print(f"右侧垂直面板高度: {right_height}")

                    # 获取垂直分割位置
                    sash_count = len(self.right_paned.panes())
                    print(f"垂直面板数量: {sash_count}")

                    if sash_count > 1:
                        heights = []
                        last_y = 0
                        for i in range(sash_count - 1):
                            try:
                                sash_y = self.right_paned.sash_coord(i)[1]
                                panel_height = sash_y - last_y
                                heights.append(panel_height)
                                print(f"面板 {i+1} 高度: {panel_height} ({panel_height/right_height*100:.1f}%)")
                                last_y = sash_y
                            except Exception as e:
                                print(f"获取垂直分割线 {i+1} 位置失败: {e}")
                                # 使用估算值
                                estimated_height = right_height // sash_count
                                heights.append(estimated_height)
                                print(f"面板 {i+1} 高度(估算): {estimated_height}")
                                last_y += estimated_height

                        # 最后一个面板
                        last_panel_height = right_height - last_y
                        heights.append(last_panel_height)
                        print(f"面板 {sash_count} 高度: {last_panel_height} ({last_panel_height/right_height*100:.1f}%)")

                        # 打印比例
                        ratio_str = ":".join(str(h) for h in heights)
                        print(f"垂直面板比例: {ratio_str}")
                    else:
                        print("只有一个垂直面板")

                except Exception as e:
                    print(f"获取垂直面板信息失败: {e}")

            print("-"*40)

        except Exception as e:
            print(f"处理面板拖动事件时出错: {e}")

        # 延迟打印初始窗口信息
        self.root.after(500, self.print_initial_window_info)
    
    def create_file_section(self, parent):
        """创建文件管理区域"""
        # 使用固定padding，不根据屏幕大小调整
        padding_value = self.scale_value(8)

        file_frame = ttk.LabelFrame(parent, text="文件管理", padding=padding_value)
        file_frame.pack(fill='both', expand=True)

        # 文件夹选择
        folder_frame = ttk.Frame(file_frame)
        folder_frame.pack(fill='x', pady=(0, self.scale_value(3)))

        # 使用固定标签文本和字体
        label_text = "文件夹(Excel/CSV):"
        label_font_size = self.scale_font_size(9)

        ttk.Label(folder_frame, text=label_text, font=('SimHei', label_font_size)).pack(side='left')

        self.folder_var = tk.StringVar()
        self.folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_var, state='readonly', font=('SimHei', label_font_size))
        self.folder_entry.pack(side='left', fill='x', expand=True, padx=(self.scale_value(3), self.scale_value(3)))

        # 使用固定按钮文本
        button_text = "选择文件夹"
        ttk.Button(folder_frame, text=button_text, command=self.select_folder).pack(side='right')

        # 加载按钮
        button_frame = ttk.Frame(file_frame)
        button_frame.pack(fill='x')

        # 使用固定按钮文本，不根据屏幕大小调整
        load_text = "加载文件(Excel/CSV)"
        self.load_button = ttk.Button(button_frame, text=load_text, command=self.load_files)
        self.load_button.pack(fill='none', side='left')

        # 注释掉生成测试数据按钮
        # ttk.Button(button_frame, text="生成测试数据", command=self.generate_test_data).pack(side='left', padx=(10, 0))

        # 文件信息显示
        self.file_info_var = tk.StringVar(value="未加载文件")
        ttk.Label(file_frame, textvariable=self.file_info_var, style='Status.TLabel').pack(anchor='w', pady=(self.scale_value(5), 0))
    
    def create_search_section(self, parent):
        """创建搜索区域"""
        # 使用固定padding，不根据屏幕大小调整
        padding_value = self.scale_value(5)

        search_frame = ttk.LabelFrame(parent, text="查找相似病例", padding=padding_value)
        search_frame.pack(fill='both', expand=True)

        # 使用固定Entry宽度，不根据屏幕大小调整
        entry_width = max(20, int(25 * self.scale_factor))

        # 使用固定字体大小，不根据屏幕大小调整
        label_font_size = self.scale_font_size(9)

        # 使用固定的水平布局，不根据屏幕大小调整
        # 计算Entry宽度
        name_width = max(15, int(25 * self.scale_factor))
        gender_width = max(8, int(12 * self.scale_factor))
        address_width = max(20, int(30 * self.scale_factor))

        # 姓名输入
        ttk.Label(search_frame, text="姓名:", font=('SimHei', label_font_size)).grid(row=0, column=0, sticky='w', pady=self.scale_value(2))
        self.search_name_var = tk.StringVar()
        name_entry = ttk.Entry(search_frame, textvariable=self.search_name_var, width=name_width, font=('SimHei', label_font_size))
        name_entry.grid(row=0, column=1, sticky='ew', padx=(self.scale_value(3), 0), pady=self.scale_value(2))
        self.setup_simple_paste_menu(name_entry, self.search_name_var)

        # 性别输入
        ttk.Label(search_frame, text="性别:", font=('SimHei', label_font_size)).grid(row=0, column=2, sticky='w', pady=self.scale_value(2), padx=(self.scale_value(5), 0))
        self.search_gender_var = tk.StringVar()
        gender_entry = ttk.Entry(search_frame, textvariable=self.search_gender_var, width=gender_width, font=('SimHei', label_font_size))
        gender_entry.grid(row=0, column=3, sticky='ew', padx=(self.scale_value(3), 0), pady=self.scale_value(2))
        self.setup_simple_paste_menu(gender_entry, self.search_gender_var)

        # 住址输入
        ttk.Label(search_frame, text="住址:", font=('SimHei', label_font_size)).grid(row=1, column=0, sticky='w', pady=self.scale_value(2))
        self.search_address_var = tk.StringVar()
        address_entry = ttk.Entry(search_frame, textvariable=self.search_address_var, width=address_width, font=('SimHei', label_font_size))
        address_entry.grid(row=1, column=1, columnspan=3, sticky='ew', padx=(self.scale_value(3), 0), pady=self.scale_value(2))
        self.setup_simple_paste_menu(address_entry, self.search_address_var)

        # 症状输入
        ttk.Label(search_frame, text="症状:", font=('SimHei', label_font_size)).grid(row=2, column=0, sticky='w', pady=self.scale_value(2))
        self.symptoms_var = tk.StringVar()
        symptoms_entry = ttk.Entry(search_frame, textvariable=self.symptoms_var, width=address_width, font=('SimHei', label_font_size))
        symptoms_entry.grid(row=2, column=1, columnspan=3, sticky='ew', padx=(self.scale_value(3), 0), pady=self.scale_value(2))
        self.setup_simple_paste_menu(symptoms_entry, self.symptoms_var)

        # 按钮框架
        button_frame = ttk.Frame(search_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=(self.scale_value(5), 0))

        # 配置网格权重
        search_frame.columnconfigure(1, weight=1)
        search_frame.columnconfigure(3, weight=1)

        # 使用固定的水平按钮布局
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)

        search_button = ttk.Button(button_frame, text="搜索病例", command=self.search_cases)
        search_button.grid(row=0, column=0, sticky='ew', padx=(0, self.scale_value(3)))

        clear_button = ttk.Button(button_frame, text="清除", command=self.clear_search_fields)
        clear_button.grid(row=0, column=1, sticky='ew')

        # 绑定回车键
        name_entry.bind('<Return>', lambda e: self.search_cases())
        gender_entry.bind('<Return>', lambda e: self.search_cases())
        address_entry.bind('<Return>', lambda e: self.search_cases())
        symptoms_entry.bind('<Return>', lambda e: self.search_cases())

    def clear_search_fields(self):
        """清除所有搜索字段"""
        self.search_name_var.set("")
        self.search_gender_var.set("")
        self.search_address_var.set("")
        self.symptoms_var.set("")

        # 清空搜索结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.current_search_results.clear()

        # 更新状态
        self.update_status("已清除搜索条件和结果")

    def create_results_section(self, parent):
        """创建结果显示区域"""
        results_frame = ttk.LabelFrame(parent, text="搜索结果", padding=self.scale_value(5))
        results_frame.pack(fill='both', expand=True)

        # 使用固定的列显示，不根据屏幕大小调整
        # 正常屏幕显示所有列（注释掉性别列）
        columns = ('就诊时间', '姓名', '年龄', '住址', '症状', '诊断结果', '处方', '用法用量', '开方人')
        base_widths = {'就诊时间': 180, '姓名': 80, '年龄': 50, '住址': 120, '症状': 180, '诊断结果': 150, '处方': 180, '用法用量': 130, '开方人': 80}
        # 注释掉性别列：'性别': 40
        tree_height = max(8, int(15 * self.scale_factor))

        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=tree_height)

        # 设置列标题和宽度（根据缩放调整）
        column_widths = {col: self.scale_value(width) for col, width in base_widths.items()}

        for col in columns:
            self.results_tree.heading(col, text=col)
            # 确保列宽不会太小
            min_width = max(25, self.scale_value(30))
            self.results_tree.column(col, width=column_widths.get(col, self.scale_value(80)), minwidth=min_width)

        # 添加滚动条
        results_scrollbar_y = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        results_scrollbar_x = ttk.Scrollbar(results_frame, orient='horizontal', command=self.results_tree.xview)

        self.results_tree.configure(yscrollcommand=results_scrollbar_y.set, xscrollcommand=results_scrollbar_x.set)

        # 布局
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        results_scrollbar_y.grid(row=0, column=1, sticky='ns')
        results_scrollbar_x.grid(row=1, column=0, sticky='ew')

        # 配置网格权重
        results_frame.rowconfigure(0, weight=1)
        results_frame.columnconfigure(0, weight=1)

        # 双击查看详情
        self.results_tree.bind('<Double-1>', self.show_case_details)

        # 添加列标题点击排序功能
        self.sort_reverse = True  # 默认倒序（时间从近到远）
        self.results_tree.heading('就诊时间', command=self.sort_by_visit_time)
    
    def create_add_record_content(self, parent):
        """直接在LabelFrame中创建添加新病例内容"""
        # 直接使用parent作为容器，不使用滚动框架
        add_frame = parent

        # 配置网格权重，让内容能够自适应宽度
        add_frame.columnconfigure(1, weight=1)

        # 设置行权重，让按钮区域固定在底部
        for i in range(12):  # 11个字段 + 1个按钮行
            if i < 11:  # 字段行
                add_frame.rowconfigure(i, weight=0)
            else:  # 按钮行
                add_frame.rowconfigure(i, weight=0)

        # 输入字段，添加开方人
        fields = [
            ('姓名:', 'name_var'),
            ('年龄:', 'age_var'),
            ('性别:', 'gender_var'),
            ('住址:', 'address_var'),
            ('联系方式:', 'contact_var'),
            ('就诊时间:', 'visit_time_var'),
            ('症状:', 'symptoms_add_var'),
            ('诊断结果:', 'diagnosis_var'),
            ('处方:', 'prescription_var'),
            ('用法用量:', 'usage_var'),
            ('开方人:', 'prescriber_var'),  # 新增开方人字段
        ]

        self.add_vars = {}
        self.add_record_entries = {}  # 新增：保存Entry/Text控件引用

        for i, (label, var_name) in enumerate(fields):
            ttk.Label(add_frame, text=label).grid(row=i, column=0, sticky='nw', pady=self.scale_value(2))  # 改为nw对齐

            if var_name in ['symptoms_add_var', 'diagnosis_var', 'prescription_var', 'usage_var']:
                self.add_vars[var_name] = tk.StringVar()

                # 调整文本框高度，宽度设为较小值让sticky='ew'生效
                if var_name == 'prescription_var':
                    height = max(4, int(6 * self.scale_factor))
                elif var_name == 'usage_var':
                    height = max(2, int(3 * self.scale_factor))
                elif var_name == 'diagnosis_var':
                    height = max(2, int(3 * self.scale_factor))
                else:  # symptoms_add_var
                    height = max(3, int(4 * self.scale_factor))

                # 使用较小的初始宽度，让sticky='ew'能够自动拉伸
                entry = tk.Text(add_frame, height=height, width=20, wrap='word', font=self.default_font)
                entry.grid(row=i, column=1, sticky='ew', padx=(self.scale_value(5), 0), pady=self.scale_value(2))
                setattr(self, f'{var_name}_widget', entry)
                self.add_record_entries[var_name] = entry
                # 为Text控件添加右键菜单
                self.setup_text_paste_menu(entry)
            elif var_name == 'prescriber_var':
                # 开方人特殊处理：显示选择框
                prescriber_frame = ttk.Frame(add_frame)
                prescriber_frame.grid(row=i, column=1, sticky='ew', padx=(self.scale_value(5), 0), pady=self.scale_value(2))

                self.add_vars[var_name] = tk.StringVar()
                prescriber_entry = ttk.Entry(prescriber_frame, textvariable=self.add_vars[var_name],
                                           state='readonly', width=max(15, int(20 * self.scale_factor)))
                prescriber_entry.pack(side='left', fill='x', expand=True)

                # 选择按钮
                select_btn = ttk.Button(prescriber_frame, text="选择", width=max(6, int(8 * self.scale_factor)),
                                      command=self.create_doctor_selection_popup(prescriber_entry, self.add_vars[var_name]))
                select_btn.pack(side='right', padx=(self.scale_value(5), 0))

                self.add_record_entries[var_name] = prescriber_frame
            elif var_name == 'visit_time_var':
                # 特殊处理就诊时间：年月日分离输入
                time_frame = ttk.Frame(add_frame)
                time_frame.grid(row=i, column=1, sticky='ew', padx=(self.scale_value(5), 0), pady=self.scale_value(2))

                # 年输入框
                self.year_var = tk.StringVar()
                year_entry = ttk.Entry(time_frame, textvariable=self.year_var, width=max(4, int(6 * self.scale_factor)))
                year_entry.pack(side='left')
                ttk.Label(time_frame, text="年").pack(side='left', padx=(self.scale_value(2), self.scale_value(5)))

                # 月输入框
                self.month_var = tk.StringVar()
                month_entry = ttk.Entry(time_frame, textvariable=self.month_var, width=max(3, int(4 * self.scale_factor)))
                month_entry.pack(side='left')
                ttk.Label(time_frame, text="月").pack(side='left', padx=(self.scale_value(2), self.scale_value(5)))

                # 日输入框
                self.day_var = tk.StringVar()
                day_entry = ttk.Entry(time_frame, textvariable=self.day_var, width=max(3, int(4 * self.scale_factor)))
                day_entry.pack(side='left')
                ttk.Label(time_frame, text="日").pack(side='left', padx=(self.scale_value(2), self.scale_value(5)))

                # 添加"今天"按钮
                def set_today():
                    """设置为今天的日期"""
                    import datetime
                    today = datetime.datetime.now()
                    self.year_var.set(str(today.year))
                    self.month_var.set(str(today.month))
                    self.day_var.set(str(today.day))

                today_button = ttk.Button(time_frame, text="今天", command=set_today, width=6)
                today_button.pack(side='left', padx=(5, 0))

                # 设置默认值为当前日期
                now = datetime.datetime.now()
                self.year_var.set(str(now.year))
                self.month_var.set(str(now.month))
                self.day_var.set(str(now.day))

                # 保存引用
                self.add_record_entries[var_name] = time_frame
                self.add_vars[var_name] = None  # 占位符，实际使用年月日变量
            else:
                self.add_vars[var_name] = tk.StringVar()

                # 使用较小的初始宽度，让sticky='ew'能够自动拉伸
                entry = ttk.Entry(add_frame, textvariable=self.add_vars[var_name], width=20)
                entry.grid(row=i, column=1, sticky='ew', padx=(self.scale_value(5), 0), pady=self.scale_value(2))
                self.add_record_entries[var_name] = entry
                # 为Entry控件添加右键菜单
                self.setup_simple_paste_menu(entry, self.add_vars[var_name])

        # 按钮组 - 使用等比例缩放
        button_frame = ttk.Frame(add_frame)
        button_frame.grid(row=len(fields), column=0, columnspan=2, pady=(self.scale_value(15), 0), sticky='ew')

        # 使用固定的水平按钮布局
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)

        add_button = ttk.Button(button_frame, text="添加病例", command=self.add_record)
        add_button.grid(row=0, column=0, sticky='ew', padx=(0, self.scale_value(5)))

        clear_button = ttk.Button(button_frame, text="清空输入", command=self.clear_add_form)
        clear_button.grid(row=0, column=1, sticky='ew')

        # 回车跳转逻辑
        self.add_record_entries['name_var'].bind('<Return>', lambda e: self.add_record_entries['age_var'].focus_set())
        self.add_record_entries['age_var'].bind('<Return>', lambda e: self.add_record_entries['gender_var'].focus_set())
        self.add_record_entries['gender_var'].bind('<Return>', lambda e: self.add_record_entries['address_var'].focus_set())
        self.add_record_entries['address_var'].bind('<Return>', lambda e: self.add_record_entries['contact_var'].focus_set())
        self.add_record_entries['contact_var'].bind('<Return>', lambda e: self.add_record_entries['symptoms_add_var'].focus_set())

        # Text控件回车跳转
        def symptoms_return(event):
            self.add_record_entries['diagnosis_var'].focus_set()
            return 'break'
        self.add_record_entries['symptoms_add_var'].bind('<Return>', symptoms_return)

        def diagnosis_return(event):
            self.add_record_entries['prescription_var'].focus_set()
            return 'break'
        self.add_record_entries['diagnosis_var'].bind('<Return>', diagnosis_return)

        def prescription_return(event):
            self.add_record_entries['usage_var'].focus_set()
            return 'break'
        self.add_record_entries['prescription_var'].bind('<Return>', prescription_return)

        def usage_return(event):
            # 跳转到开方人选择框或添加按钮
            add_button.focus_set()
            return 'break'
        self.add_record_entries['usage_var'].bind('<Return>', usage_return)


    def create_doctor_selection_popup(self, entry_widget, prescriber_var):
        """创建医生选择弹出窗口"""
        def show_doctor_selection():
            popup = tk.Toplevel(self.root)
            popup.title("选择开方人")
            popup.geometry("300x400")
            popup.resizable(False, False)
            
            # 设置为模态窗口
            popup.transient(self.root)
            popup.grab_set()
            
            # 居中显示
            popup.update_idletasks()
            x = (popup.winfo_screenwidth() // 2) - (popup.winfo_width() // 2)
            y = (popup.winfo_screenheight() // 2) - (popup.winfo_height() // 2)
            popup.geometry(f"+{x}+{y}")
            
            # 创建主框架
            main_frame = ttk.Frame(popup, padding=10)
            main_frame.pack(fill='both', expand=True)
            
            # 标题
            title_label = ttk.Label(main_frame, text="请选择开方人：", font=('SimHei', 14, 'bold'))
            title_label.pack(pady=(0, 10))
            
            # 创建按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill='both', expand=True)
            
            # 为每个医生创建按钮
            for i, doctor in enumerate(self.doctors_list):
                def select_doctor(doc_name=doctor):
                    prescriber_var.set(doc_name)
                    popup.destroy()
                
                doctor_btn = ttk.Button(button_frame, text=doctor, command=select_doctor)
                doctor_btn.pack(fill='x', pady=2)
            
            # 清空选择按钮
            def clear_selection():
                prescriber_var.set("")
                popup.destroy()
            
            clear_btn = ttk.Button(button_frame, text="清空选择", command=clear_selection)
            clear_btn.pack(fill='x', pady=(10, 5))
            
            # 取消按钮
            cancel_btn = ttk.Button(button_frame, text="取消", command=popup.destroy)
            cancel_btn.pack(fill='x', pady=(0, 0))
        
        return show_doctor_selection
    
    def create_status_section(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill='x', side='bottom', padx=self.scale_value(10), pady=(0, self.scale_value(5)))

        self.status_var = tk.StringVar()
        status_label = ttk.Label(status_frame, textvariable=self.status_var, relief='sunken', anchor='w')
        status_label.pack(fill='x')
    
    def select_folder(self):
        """选择Excel文件夹"""
        folder = filedialog.askdirectory(title="选择包含Excel文件的文件夹（可以是空文件夹）")
        if folder:
            self.excel_folder_path = folder
            self.folder_var.set(folder)
            self.update_status(f"已选择文件夹: {folder}")
    
    def load_files(self):
        """加载Excel和CSV文件（带进度条）- 智能加载文件夹和打包文件"""
        if not self.excel_folder_path:
            messagebox.showwarning("警告", "请先选择文件夹!")
            return
        
        # 创建进度窗口
        self.progress_window = self.create_progress_window()
        self.load_button.config(state='disabled')
        self.is_loading_cancelled = False
        
        def load_thread():
            try:
                self.manager = MedicalRecordsManager(self.excel_folder_path)
                
                # 设置进度回调
                def progress_callback(current, total, message):
                    if self.is_loading_cancelled:
                        return False
                    
                    progress = (current / total * 100) if total > 0 else 0
                    self.root.after(0, lambda: self.update_progress(progress, message))
                    return True
                
                self.manager.set_progress_callback(progress_callback)
                
                # 第1步：扫描文件夹中的文件
                excel_files = glob.glob(os.path.join(self.excel_folder_path, "*.xlsx")) + \
                             glob.glob(os.path.join(self.excel_folder_path, "*.xls"))
                csv_files = glob.glob(os.path.join(self.excel_folder_path, "*.csv"))
                
                folder_files = excel_files + csv_files
                
                # 第2步：检查是否有打包的CSV文件
                packaged_csv = get_resource_path("ceshi_real data_converted.csv")  # 修改这里
                has_packaged_csv = os.path.exists(packaged_csv)
                
                # 第3步：计算总的加载任务
                total_tasks = len(folder_files) + (1 if has_packaged_csv else 0)
                
                if total_tasks == 0:
                    self.root.after(0, lambda: messagebox.showerror("错误", "没有找到任何数据文件！"))
                    self.root.after(0, lambda: self.update_status("没有找到数据文件"))
                    return
                
                # 第4步：开始加载
                current_task = 0
                
                # 加载文件夹中的文件
                if folder_files:
                    self.root.after(0, lambda: self.update_progress(0, f"正在加载文件夹中的 {len(folder_files)} 个文件..."))
                    success = self.manager.load_excel_files_with_progress()
                    
                    if not success and self.is_loading_cancelled:
                        self.root.after(0, lambda: self.update_status("加载已取消"))
                        return
                    
                    current_task += len(folder_files)
                
                # 加载打包的CSV文件
                if has_packaged_csv:
                    progress = (current_task / total_tasks * 100) if total_tasks > 0 else 0
                    self.root.after(0, lambda: self.update_progress(progress, "正在加载打包的数据文件..."))
                    
                    try:
                        self.manager.load_csv_file(packaged_csv, "内置数据文件")
                        current_task += 1
                    except Exception as e:
                        error_msg = f"加载打包数据文件时出错: {str(e)}"
                        self.manager.extraction_errors.append(error_msg)
                
                if not self.is_loading_cancelled:
                    # 统计加载结果
                    folder_file_count = len(folder_files)
                    total_records = len(self.manager.all_records)
                    
                    # 生成加载结果信息
                    if folder_file_count > 0 and has_packaged_csv:
                        info_text = f"已加载 {folder_file_count} 个文件夹文件 + 1 个内置文件，共 {total_records} 条记录"
                        status_text = "文件夹文件和内置文件加载完成"
                    elif folder_file_count > 0:
                        info_text = f"已加载 {folder_file_count} 个文件夹文件，共 {total_records} 条记录"
                        status_text = "文件夹文件加载完成"
                    elif has_packaged_csv:
                        info_text = f"已加载 1 个内置文件，共 {total_records} 条记录"
                        status_text = "内置文件加载完成"
                    else:
                        info_text = "未加载任何文件"
                        status_text = "加载失败"
                    
                    self.root.after(0, lambda: self.file_info_var.set(info_text))
                    self.root.after(0, lambda: self.update_status(status_text))
                    
                    # 显示加载详情
                    if total_records > 0:
                        detail_msg = f"加载完成！\n\n"
                        if folder_file_count > 0:
                            detail_msg += f"📁 文件夹文件：{folder_file_count} 个\n"
                        if has_packaged_csv:
                            detail_msg += f"📦 内置数据文件：1 个\n"
                        detail_msg += f"📊 总记录数：{total_records} 条"
                        
                        self.root.after(0, lambda: messagebox.showinfo("加载成功", detail_msg))
                    
                    # 显示提取错误信息（如果有）
                    if self.manager.extraction_errors:
                        self.root.after(0, lambda: self.show_extraction_errors())
                        
            except Exception as e:
                if not self.is_loading_cancelled:
                    self.root.after(0, lambda: messagebox.showerror("错误", f"加载文件时出错: {str(e)}"))
                    self.root.after(0, lambda: self.update_status("加载出错"))
            finally:
                self.root.after(0, lambda: self.close_progress_window())
                self.root.after(0, lambda: self.load_button.config(state='normal'))
        
        threading.Thread(target=load_thread, daemon=True).start()
    
    def create_progress_window(self):
        """创建进度显示窗口"""
        progress_window = tk.Toplevel(self.root)
        progress_window.title("加载进度")
        progress_window.geometry("500x250")  # 增加高度以完全显示取消按钮
        progress_window.resizable(False, False)
        
        # 居中显示
        progress_window.transient(self.root)
        progress_window.grab_set()
        
        # 禁用关闭按钮
        progress_window.protocol("WM_DELETE_WINDOW", lambda: None)
        
        # 主框架
        main_frame = ttk.Frame(progress_window, padding=20)
        main_frame.pack(fill='both', expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="正在加载文件...", font=('SimHei', 14, 'bold'))
        title_label.pack(pady=(0, 15))
        
        # 进度条
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(main_frame, variable=progress_var, maximum=100, length=400)
        progress_bar.pack(pady=(0, 10))
        
        # 进度文本
        progress_text_var = tk.StringVar(value="准备开始...")
        progress_label = ttk.Label(main_frame, textvariable=progress_text_var)
        progress_label.pack(pady=(0, 15))
        
        # 详细状态
        status_var = tk.StringVar(value="")
        status_label = ttk.Label(main_frame, textvariable=status_var, font=('SimHei', 10))
        status_label.pack(pady=(0, 15))
        
        # 取消按钮
        def cancel_loading():
            self.is_loading_cancelled = True
            cancel_button.config(state='disabled', text="正在取消...")
        
        cancel_button = ttk.Button(main_frame, text="取消", command=cancel_loading)
        cancel_button.pack()
        
        # 保存引用
        progress_window.progress_var = progress_var
        progress_window.progress_text_var = progress_text_var
        progress_window.status_var = status_var
        progress_window.cancel_button = cancel_button
        
        return progress_window
    
    def update_progress(self, progress, message):
        """更新进度条"""
        if hasattr(self, 'progress_window') and self.progress_window:
            self.progress_window.progress_var.set(progress)
            self.progress_window.progress_text_var.set(f"进度: {progress:.1f}%")
            self.progress_window.status_var.set(message)
    
    def close_progress_window(self):
        """关闭进度窗口"""
        if hasattr(self, 'progress_window') and self.progress_window:
            self.progress_window.destroy()
            self.progress_window = None
    
    def show_extraction_errors(self):
        """显示数据提取过程中的错误信息"""
        if not self.manager.extraction_errors:
            return
            
        # 创建错误信息窗口
        error_window = tk.Toplevel(self.root)
        error_window.title("数据提取警告")
        error_window.geometry("800x600")
        error_window.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(error_window)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="数据提取过程中遇到的问题：", font=('SimHei', 14, 'bold'))
        title_label.pack(anchor='w', pady=(0, 10))
        
        # 创建滚动文本框显示错误信息
        text_widget = scrolledtext.ScrolledText(main_frame, wrap='word', font=('SimHei', 12))
        text_widget.pack(fill='both', expand=True, pady=(0, 10))
        
        # 插入错误信息
        error_text = "\n".join([f"• {error}" for error in self.manager.extraction_errors])
        text_widget.insert('1.0', error_text)
        text_widget.config(state='disabled')
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x')
        
        # 关闭按钮
        close_button = ttk.Button(button_frame, text="确定", command=error_window.destroy)
        close_button.pack(side='right')
        
        # 提示信息
        info_label = ttk.Label(button_frame, text="注意：系统已成功加载有效数据，上述问题不影响正常使用。", 
                              foreground='blue', font=('SimHei', 10))
        info_label.pack(side='left')
    
    def search_cases(self):
        """搜索相似病例"""
        if not self.manager:
            messagebox.showwarning("警告", "请先加载Excel文件!")
            return

        name = self.search_name_var.get().strip()
        gender = self.search_gender_var.get().strip()
        address = self.search_address_var.get().strip()
        symptoms = self.symptoms_var.get().strip()
        condition = self.condition_var.get().strip()

        if not (name or gender or address or symptoms or condition):
            messagebox.showwarning("警告", "请至少输入一个搜索条件!")
            return

        # 清空之前的结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.current_search_results.clear()
        self.update_status("正在搜索相似病例...")

        # 显示加载对话框
        loading_dialog = LoadingDialog(self.root, "搜索中", "正在搜索相似病例，请稍候...")

        # 在新线程中执行搜索
        def search_thread():
            try:
                similar_cases = self.manager.search_similar_cases(name, gender, address, symptoms, condition)
                # 在主线程中更新UI
                self.root.after(0, lambda: self.handle_search_results(similar_cases, loading_dialog))
            except Exception as e:
                # 在主线程中处理错误
                error_msg = str(e)  # 先保存错误信息
                self.root.after(0, lambda: self.handle_search_error(error_msg, loading_dialog))

        # 启动搜索线程
        threading.Thread(target=search_thread, daemon=True).start()

    def handle_search_results(self, similar_cases, loading_dialog):
        """处理搜索结果"""
        try:
            # 关闭加载对话框
            loading_dialog.close()
            
            if similar_cases:
                for i, (case, similarity) in enumerate(similar_cases):
                    # 生成唯一ID
                    result_id = f"result_{i}"
                    
                    # 存储完整的病例数据
                    self.current_search_results[result_id] = case

                    # 合并症状和病情
                    symptoms = case.get('symptoms', '').strip()
                    condition = case.get('condition', '').strip()

                    # 合并症状和病情，避免重复显示相同内容
                    if symptoms and condition and symptoms == condition:
                        # 如果症状和病情内容相同，只显示一次，不加前缀
                        symptoms_condition_text = symptoms
                    else:
                        # 如果内容不同，合并显示，不加"症状："前缀
                        symptoms_condition_parts = []
                        if symptoms:
                            symptoms_condition_parts.append(symptoms)
                        if condition and condition != symptoms:
                            symptoms_condition_parts.append(condition)
                        symptoms_condition_text = ' | '.join(symptoms_condition_parts) if symptoms_condition_parts else '未知'

                    # 限制文本长度以适应显示
                    if len(symptoms_condition_text) > 50:
                        symptoms_condition_display = symptoms_condition_text[:50] + '...'
                    else:
                        symptoms_condition_display = symptoms_condition_text

                    # 处理处方信息
                    prescription = case.get('prescription', '').strip()
                    prescription_display = prescription[:50] + '...' if len(prescription) > 50 else prescription
                    if not prescription_display:
                        prescription_display = '未知'

                    # 处理用法用量
                    usage = case.get('usage', '').strip()
                    usage_display = usage[:30] + '...' if len(usage) > 30 else usage
                    if not usage_display:
                        usage_display = '未知'

                    # 处理住址
                    address_text = case.get('address', '未知')[:25] + '...' if len(case.get('address', '')) > 25 else case.get('address', '未知')

                    # 处理诊断结果
                    diagnosis = case.get('diagnosis', '').strip()
                    diagnosis_display = diagnosis[:40] + '...' if len(diagnosis) > 40 else diagnosis
                    if not diagnosis_display:
                        diagnosis_display = '未知'

                    # 处理开方人信息
                    prescriber = case.get('prescriber', '').strip()
                    prescriber_display = prescriber if prescriber else '未知'

                    # 使用固定的完整列显示，不根据屏幕大小调整（注释掉性别列）
                    values = (
                        self.format_visit_time(case.get('visit_time', '未知')),
                        case.get('name', '未知'),
                        case.get('age', '未知'),
                        # case.get('gender', '未知'),  # 注释掉性别列
                        address_text,
                        symptoms_condition_display,
                        diagnosis_display,
                        prescription_display,
                        usage_display,
                        prescriber_display
                    )

                    # 插入到Treeview，使用result_id作为item的ID
                    self.results_tree.insert('', 'end', iid=result_id, values=values)
                
                self.update_status(f"找到 {len(similar_cases)} 个相似病例")
            else:
                self.update_status("没有找到相似的病例")
                messagebox.showinfo("提示", "没有找到相似的病例")
                
        except Exception as e:
            messagebox.showerror("错误", f"搜索时出错: {str(e)}")
            self.update_status("搜索出错")

    def handle_search_error(self, error_message, loading_dialog):
        """处理搜索错误"""
        try:
            # 关闭加载对话框
            loading_dialog.close()

            # 显示错误信息
            messagebox.showerror("错误", f"搜索时出错: {error_message}")
            self.update_status("搜索出错")
        except Exception as e:
            # 如果处理错误时也出错，至少要关闭加载对话框
            try:
                loading_dialog.close()
            except:
                pass
            messagebox.showerror("错误", f"搜索时出错: {error_message}")

    def format_contact(self, contact):
        """格式化联系方式，移除.0后缀"""
        if not contact or contact == '未知':
            return '未知'

        contact_str = str(contact)
        # 如果是浮点数格式（如13603853128.0），转换为整数字符串
        if contact_str.endswith('.0'):
            try:
                return str(int(float(contact_str)))
            except (ValueError, TypeError):
                return contact_str
        return contact_str

    def format_visit_time(self, visit_time):
        """格式化就诊时间，将2025-07-09格式转换为2025.7.9格式"""
        if not visit_time or visit_time == '未知':
            return '未知'

        visit_time_str = str(visit_time).strip()

        # 处理 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式
        if '-' in visit_time_str:
            try:
                # 分离日期和时间部分
                if ' ' in visit_time_str:
                    date_part, time_part = visit_time_str.split(' ', 1)
                else:
                    date_part = visit_time_str
                    time_part = None

                # 处理日期部分：YYYY-MM-DD -> YYYY.M.D
                date_parts = date_part.split('-')
                if len(date_parts) == 3:
                    year, month, day = date_parts
                    # 去掉前导零
                    month = str(int(month))
                    day = str(int(day))
                    formatted_date = f"{year}.{month}.{day}"

                    # 如果有时间部分，保留时间
                    if time_part:
                        return f"{formatted_date} {time_part}"
                    else:
                        return formatted_date
            except (ValueError, IndexError):
                pass

        return visit_time_str

    def setup_text_selection_copy(self, text_widget, parent_window):
        """为文本控件设置选中文字复制功能"""
        # 创建浮动复制按钮（初始隐藏）
        copy_popup = tk.Toplevel(parent_window)
        copy_popup.withdraw()  # 初始隐藏
        copy_popup.overrideredirect(True)  # 无边框窗口
        copy_popup.attributes('-topmost', True)  # 置顶

        copy_btn = tk.Button(copy_popup, text="复制", font=('Arial', 14, 'bold'),
                           bg='lightblue', fg='black', relief='raised', bd=2,
                           width=6, height=1, cursor='hand2',
                           padx=8, pady=4,  # 按钮内文字的内边距
                           command=lambda: self.copy_selected_text(text_widget, copy_popup))
        copy_btn.pack(padx=6, pady=6)  # 按钮外部的间距

        def on_text_select(event):
            """当文本被选中时显示复制按钮"""
            try:
                # 检查是否有选中的文本
                if text_widget.tag_ranges(tk.SEL):
                    # 获取鼠标位置，并增加更大的偏移量避免遮挡
                    x = text_widget.winfo_rootx() + event.x + 50  # 增加水平偏移
                    y = text_widget.winfo_rooty() + event.y - 50  # 增加垂直偏移

                    # 确保按钮不会超出屏幕边界
                    screen_width = text_widget.winfo_screenwidth()
                    screen_height = text_widget.winfo_screenheight()

                    # 如果按钮会超出右边界，则显示在左侧
                    if x + 100 > screen_width:
                        x = text_widget.winfo_rootx() + event.x - 100

                    # 如果按钮会超出上边界，则显示在下方
                    if y < 0:
                        y = text_widget.winfo_rooty() + event.y + 30

                    # 显示复制按钮
                    copy_popup.geometry(f"+{x}+{y}")
                    copy_popup.deiconify()
                else:
                    copy_popup.withdraw()
            except tk.TclError:
                copy_popup.withdraw()

        def on_click_elsewhere(event):
            """点击其他地方时隐藏复制按钮"""
            copy_popup.withdraw()

        # 绑定事件
        text_widget.bind('<ButtonRelease-1>', on_text_select)
        text_widget.bind('<B1-Motion>', on_text_select)
        parent_window.bind('<Button-1>', on_click_elsewhere)

        # 当详情窗口关闭时，也关闭复制按钮窗口
        def on_detail_close():
            copy_popup.destroy()
            parent_window.destroy()

        parent_window.protocol("WM_DELETE_WINDOW", on_detail_close)

    def copy_selected_text(self, text_widget, copy_popup):
        """复制选中的文本到剪贴板"""
        try:
            selected_text = text_widget.get(tk.SEL_FIRST, tk.SEL_LAST)
            if selected_text:
                text_widget.clipboard_clear()
                text_widget.clipboard_append(selected_text)

                # 短暂显示复制成功提示
                copy_popup.withdraw()

                # 创建临时提示
                tip = tk.Toplevel(copy_popup.master)
                tip.overrideredirect(True)
                tip.attributes('-topmost', True)
                tip.configure(bg='green')

                tip_label = tk.Label(tip, text="已复制!", bg='green', fg='white',
                                   font=('Arial', 10, 'bold'))
                tip_label.pack(padx=8, pady=4)

                # 获取复制按钮的位置来显示提示
                x = copy_popup.winfo_x()
                y = copy_popup.winfo_y() - 25
                tip.geometry(f"+{x}+{y}")

                # 1秒后自动关闭提示
                tip.after(1000, tip.destroy)

        except tk.TclError:
            copy_popup.withdraw()

    def setup_right_click_menu(self, widget, field_name):
        """为输入控件设置右键菜单"""
        def show_context_menu(event):
            # 只有在有复制数据时才显示菜单
            if not self.copied_case_data:
                return

            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)

            # 根据字段类型添加相应的粘贴选项
            field_mapping = {
                'name_var': ('姓名', 'name'),
                'age_var': ('年龄', 'age'),
                'gender_var': ('性别', 'gender'),
                'address_var': ('住址', 'address'),
                'contact_var': ('联系方式', 'contact'),
                'symptoms_add_var': ('症状', 'symptoms'),
                'diagnosis_var': ('诊断结果', 'diagnosis'),
                'prescription_var': ('处方', 'prescription'),
                'usage_var': ('用法用量', 'usage'),
                'prescriber_var': ('开方人', 'prescriber')
            }

            if field_name in field_mapping:
                field_display_name, field_key = field_mapping[field_name]
                field_value = self.copied_case_data.get(field_key, '')

                if field_value:
                    context_menu.add_command(
                        label=f"粘贴{field_display_name}: {field_value[:20]}{'...' if len(str(field_value)) > 20 else ''}",
                        command=lambda: self.paste_field_data(widget, field_name, field_key)
                    )

            # 添加粘贴所有信息的选项
            context_menu.add_separator()
            context_menu.add_command(
                label="粘贴所有病例信息",
                command=lambda: self.paste_all_case_data()
            )

            # 显示菜单
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        # 绑定右键事件
        widget.bind("<Button-3>", show_context_menu)  # Windows右键
        widget.bind("<Button-2>", show_context_menu)  # Mac右键（如果需要）

    def setup_simple_paste_menu(self, widget, string_var):
        """为输入控件设置完整的右键菜单（包含复制、粘贴等功能）"""
        def show_context_menu(event):
            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)

            # 添加复制选项
            context_menu.add_command(
                label="复制",
                command=lambda: self.copy_from_entry(widget)
            )

            # 添加粘贴选项
            context_menu.add_command(
                label="粘贴",
                command=lambda: self.paste_from_clipboard(string_var)
            )

            # 添加全选选项
            context_menu.add_command(
                label="全选",
                command=lambda: widget.select_range(0, 'end')
            )

            # 添加清空选项
            context_menu.add_separator()
            context_menu.add_command(
                label="清空",
                command=lambda: string_var.set("")
            )

            # 显示菜单
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        # 绑定右键事件
        widget.bind("<Button-3>", show_context_menu)  # Windows右键
        widget.bind("<Button-2>", show_context_menu)  # Mac右键（如果需要）

    def copy_from_entry(self, widget):
        """从Entry控件复制选中的文本到剪贴板"""
        try:
            # 尝试获取选中的文本
            if widget.selection_present():
                selected_text = widget.selection_get()
            else:
                # 如果没有选中文本，复制全部内容
                selected_text = widget.get()

            if selected_text:
                widget.clipboard_clear()
                widget.clipboard_append(selected_text)
                # 可选：显示复制成功提示
                # messagebox.showinfo("提示", "已复制到剪贴板")
        except tk.TclError:
            # 如果没有选中文本，复制全部内容
            try:
                all_text = widget.get()
                if all_text:
                    widget.clipboard_clear()
                    widget.clipboard_append(all_text)
            except:
                pass

    def paste_from_clipboard(self, string_var):
        """从剪贴板粘贴内容到指定的StringVar"""
        try:
            # 获取剪贴板内容
            clipboard_content = self.root.clipboard_get()
            if clipboard_content:
                # 清理内容（去除首尾空白字符）
                cleaned_content = clipboard_content.strip()
                # 设置到对应的变量
                string_var.set(cleaned_content)
        except tk.TclError:
            # 剪贴板为空或无法访问
            messagebox.showwarning("提示", "剪贴板为空或无法访问")
        except Exception as e:
            messagebox.showerror("错误", f"粘贴时出错: {str(e)}")

    def setup_text_paste_menu(self, text_widget):
        """为Text控件设置完整的右键菜单（包含复制、粘贴等功能）"""
        def show_text_context_menu(event):
            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)

            # 添加复制选项
            context_menu.add_command(
                label="复制",
                command=lambda: self.copy_from_text_widget(text_widget)
            )

            # 添加粘贴选项
            context_menu.add_command(
                label="粘贴",
                command=lambda: self.paste_to_text_widget(text_widget)
            )

            # 添加全选选项
            context_menu.add_command(
                label="全选",
                command=lambda: text_widget.tag_add('sel', '1.0', 'end')
            )

            # 添加清空选项
            context_menu.add_separator()
            context_menu.add_command(
                label="清空",
                command=lambda: text_widget.delete('1.0', 'end')
            )

            # 显示菜单
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        # 绑定右键事件
        text_widget.bind("<Button-3>", show_text_context_menu)  # Windows右键
        text_widget.bind("<Button-2>", show_text_context_menu)  # Mac右键（如果需要）

    def copy_from_text_widget(self, text_widget):
        """从Text控件复制选中的文本到剪贴板"""
        try:
            # 尝试获取选中的文本
            if text_widget.tag_ranges('sel'):
                selected_text = text_widget.get('sel.first', 'sel.last')
            else:
                # 如果没有选中文本，复制全部内容
                selected_text = text_widget.get('1.0', 'end-1c')

            if selected_text:
                text_widget.clipboard_clear()
                text_widget.clipboard_append(selected_text)
                # 可选：显示复制成功提示
                # messagebox.showinfo("提示", "已复制到剪贴板")
        except tk.TclError:
            # 如果没有选中文本，复制全部内容
            try:
                all_text = text_widget.get('1.0', 'end-1c')
                if all_text:
                    text_widget.clipboard_clear()
                    text_widget.clipboard_append(all_text)
            except:
                pass

    def paste_to_text_widget(self, text_widget):
        """从剪贴板粘贴内容到Text控件"""
        try:
            # 获取剪贴板内容
            clipboard_content = self.root.clipboard_get()
            if clipboard_content:
                # 清理内容（去除首尾空白字符）
                cleaned_content = clipboard_content.strip()
                # 清空原内容并插入新内容
                text_widget.delete('1.0', 'end')
                text_widget.insert('1.0', cleaned_content)
        except tk.TclError:
            # 剪贴板为空或无法访问
            messagebox.showwarning("提示", "剪贴板为空或无法访问")
        except Exception as e:
            messagebox.showerror("错误", f"粘贴时出错: {str(e)}")

    def setup_search_right_click_menu(self, widget, field_name):
        """为搜索输入控件设置右键菜单"""
        def show_search_context_menu(event):
            # 只有在有复制数据时才显示菜单
            if not self.copied_case_data:
                return

            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)

            # 根据搜索字段类型添加相应的粘贴选项
            search_field_mapping = {
                'search_name_var': ('姓名', 'name'),
                'search_gender_var': ('性别', 'gender'),
                'search_address_var': ('住址', 'address'),
                'symptoms_var': ('症状', 'symptoms')
            }

            if field_name in search_field_mapping:
                field_display_name, field_key = search_field_mapping[field_name]
                field_value = self.copied_case_data.get(field_key, '')

                if field_value:
                    context_menu.add_command(
                        label=f"粘贴{field_display_name}: {field_value[:20]}{'...' if len(str(field_value)) > 20 else ''}",
                        command=lambda: self.paste_search_field_data(widget, field_name, field_key)
                    )

            # 添加粘贴所有搜索信息的选项
            context_menu.add_separator()
            context_menu.add_command(
                label="粘贴基本搜索信息",
                command=lambda: self.paste_all_search_data()
            )

            # 显示菜单
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        # 绑定右键事件
        widget.bind("<Button-3>", show_search_context_menu)  # Windows右键
        widget.bind("<Button-2>", show_search_context_menu)  # Mac右键（如果需要）

    def paste_search_field_data(self, widget, field_name, field_key):
        """粘贴单个搜索字段的数据"""
        if not self.copied_case_data:
            return

        field_value = self.copied_case_data.get(field_key, '')
        if not field_value:
            return

        try:
            # 根据字段名设置对应的StringVar
            if field_name == 'search_name_var':
                self.search_name_var.set(str(field_value))
            elif field_name == 'search_gender_var':
                self.search_gender_var.set(str(field_value))
            elif field_name == 'search_address_var':
                self.search_address_var.set(str(field_value))
            elif field_name == 'symptoms_var':
                # 对于症状，可能需要合并症状和病情
                symptoms = self.copied_case_data.get('symptoms', '').strip()
                condition = self.copied_case_data.get('condition', '').strip()

                if symptoms and condition and symptoms != condition:
                    combined_symptoms = f"{symptoms} {condition}"
                else:
                    combined_symptoms = symptoms or condition

                self.symptoms_var.set(combined_symptoms)

        except Exception as e:
            messagebox.showerror("错误", f"粘贴搜索数据时出错: {str(e)}")

    def paste_all_search_data(self):
        """粘贴所有搜索相关数据"""
        if not self.copied_case_data:
            return

        try:
            case = self.copied_case_data

            # 填充搜索框
            self.search_name_var.set(case.get('name', ''))
            self.search_gender_var.set(case.get('gender', ''))
            self.search_address_var.set(case.get('address', ''))

            # 合并症状和病情到症状搜索框
            symptoms = case.get('symptoms', '').strip()
            condition = case.get('condition', '').strip()

            if symptoms and condition and symptoms != condition:
                combined_symptoms = f"{symptoms} {condition}"
            else:
                combined_symptoms = symptoms or condition

            self.symptoms_var.set(combined_symptoms)

        except Exception as e:
            messagebox.showerror("错误", f"粘贴所有搜索数据时出错: {str(e)}")

    def paste_field_data(self, widget, field_name, field_key):
        """粘贴单个字段的数据"""
        if not self.copied_case_data:
            return

        field_value = self.copied_case_data.get(field_key, '')
        if not field_value:
            return

        try:
            # 根据控件类型进行不同的处理
            if isinstance(widget, tk.Text):
                # Text控件
                widget.delete('1.0', 'end')
                widget.insert('1.0', str(field_value))
            elif hasattr(widget, 'set'):
                # StringVar控件（Entry）
                if field_name in self.add_vars:
                    self.add_vars[field_name].set(str(field_value))
            elif isinstance(widget, ttk.Entry):
                # Entry控件
                widget.delete(0, 'end')
                widget.insert(0, str(field_value))

            # 特殊处理联系方式格式化
            if field_key == 'contact':
                formatted_contact = self.format_contact(field_value)
                if field_name in self.add_vars:
                    self.add_vars[field_name].set(formatted_contact)

            # 特殊处理就诊时间
            if field_key == 'visit_time' and hasattr(self, 'year_var'):
                visit_time = str(field_value).strip()
                if '.' in visit_time:
                    parts = visit_time.split('.')
                    if len(parts) >= 3:
                        self.year_var.set(parts[0])
                        self.month_var.set(parts[1])
                        self.day_var.set(parts[2])
                elif '-' in visit_time:
                    date_part = visit_time.split(' ')[0]
                    parts = date_part.split('-')
                    if len(parts) >= 3:
                        self.year_var.set(parts[0])
                        self.month_var.set(str(int(parts[1])))
                        self.day_var.set(str(int(parts[2])))

        except Exception as e:
            messagebox.showerror("错误", f"粘贴数据时出错: {str(e)}")

    def paste_all_case_data(self):
        """粘贴所有病例数据"""
        if not self.copied_case_data:
            return

        try:
            # 调用原有的复制方法，但不关闭详情窗口
            case = self.copied_case_data

            # 复制基本信息到Entry控件
            self.add_vars['name_var'].set(case.get('name', ''))
            self.add_vars['age_var'].set(case.get('age', ''))
            self.add_vars['gender_var'].set(case.get('gender', ''))
            self.add_vars['address_var'].set(case.get('address', ''))
            self.add_vars['contact_var'].set(self.format_contact(case.get('contact', '')))

            # 处理就诊时间
            visit_time = case.get('visit_time', '')
            if visit_time and visit_time != '未知':
                visit_time_str = str(visit_time).strip()
                if '.' in visit_time_str:
                    parts = visit_time_str.split('.')
                    if len(parts) >= 3:
                        self.year_var.set(parts[0])
                        self.month_var.set(parts[1])
                        self.day_var.set(parts[2])
                elif '-' in visit_time_str:
                    date_part = visit_time_str.split(' ')[0]
                    parts = date_part.split('-')
                    if len(parts) >= 3:
                        self.year_var.set(parts[0])
                        self.month_var.set(str(int(parts[1])))
                        self.day_var.set(str(int(parts[2])))

            # 复制症状病情到Text控件
            self.symptoms_add_var_widget.delete('1.0', 'end')
            symptoms = case.get('symptoms', '').strip()
            condition = case.get('condition', '').strip()

            if symptoms and condition and symptoms == condition:
                symptoms_condition_text = symptoms
            else:
                symptoms_condition_parts = []
                if symptoms:
                    symptoms_condition_parts.append(f"症状：{symptoms}")
                if condition and condition != symptoms:
                    symptoms_condition_parts.append(f"病情：{condition}")
                symptoms_condition_text = '\n'.join(symptoms_condition_parts)

            if symptoms_condition_text:
                self.symptoms_add_var_widget.insert('1.0', symptoms_condition_text)

            # 复制诊断结果
            self.diagnosis_var_widget.delete('1.0', 'end')
            diagnosis = case.get('diagnosis', '')
            if diagnosis:
                self.diagnosis_var_widget.insert('1.0', diagnosis)

            # 复制处方
            self.prescription_var_widget.delete('1.0', 'end')
            prescription = case.get('prescription', '')
            if prescription:
                self.prescription_var_widget.insert('1.0', prescription)

            # 复制用法用量
            self.usage_var_widget.delete('1.0', 'end')
            usage = case.get('usage', '')
            if usage:
                self.usage_var_widget.insert('1.0', usage)

            # 复制开方人
            prescriber = case.get('prescriber', '')
            if prescriber:
                self.add_vars['prescriber_var'].set(prescriber)

            # 将焦点设置到姓名输入框
            self.add_record_entries['name_var'].focus_set()

        except Exception as e:
            messagebox.showerror("错误", f"粘贴所有数据时出错: {str(e)}")

    def center_detail_window(self, detail_window, window_width, window_height):
        """将详情窗口居中显示"""
        detail_window.update_idletasks()

        # 获取父窗口位置和大小
        parent_x = self.root.winfo_rootx()
        parent_y = self.root.winfo_rooty()
        parent_width = self.root.winfo_width()
        parent_height = self.root.winfo_height()

        # 计算位置 - 水平居中，垂直上移
        x = parent_x + (parent_width - window_width) // 2  # 水平居中
        y = parent_y + (parent_height - window_height) // 4  # 垂直位置在1/4处，实现上移效果

        # 确保窗口不会超出屏幕边界
        screen_width = detail_window.winfo_screenwidth()
        screen_height = detail_window.winfo_screenheight()

        # 调整位置以确保窗口完全可见
        if x < 0:
            x = 0
        elif x + window_width > screen_width:
            x = screen_width - window_width

        if y < 0:
            y = 0
        elif y + window_height > screen_height:
            y = screen_height - window_height

        detail_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def parse_visit_time(self, visit_time_str):
        """解析就诊时间字符串为可比较的日期对象"""
        try:
            # 处理格式如 "2024.1.3" 或 "2024.12.27"
            parts = visit_time_str.split('.')
            if len(parts) == 3:
                year = int(parts[0])
                month = int(parts[1])
                day = int(parts[2])
                return (year, month, day)
        except:
            pass
        return (0, 0, 0)  # 无法解析时返回最小值

    def sort_by_visit_time(self):
        """按就诊时间排序搜索结果"""
        # 获取当前所有项目
        items = []
        for child in self.results_tree.get_children():
            item = self.results_tree.item(child)
            items.append((child, item['values']))

        # 按就诊时间排序
        items.sort(key=lambda x: self.parse_visit_time(x[1][0]), reverse=self.sort_reverse)

        # 清空树形控件
        for child in self.results_tree.get_children():
            self.results_tree.delete(child)

        # 重新插入排序后的项目
        for _, values in items:
            self.results_tree.insert('', 'end', values=values)

        # 切换排序顺序
        self.sort_reverse = not self.sort_reverse

        # 更新列标题显示排序状态
        if self.sort_reverse:
            self.results_tree.heading('就诊时间', text='就诊时间 ↓')
        else:
            self.results_tree.heading('就诊时间', text='就诊时间 ↑')

    def show_case_details(self, event):
        """显示病例详情"""
        selection = self.results_tree.selection()
        if not selection:
            return

        # 获取选中项的ID
        item_id = selection[0]

        # 从存储的数据中获取完整病例信息
        if item_id not in self.current_search_results:
            messagebox.showerror("错误", "无法获取病例详细信息")
            return

        case = self.current_search_results[item_id]

        # 创建详情窗口（根据缩放调整大小）
        detail_window = tk.Toplevel(self.root)
        detail_window.title("病例详情")

        # 使用固定的详情窗口尺寸
        window_width = self.scale_value(700)
        window_height = self.scale_value(750)

        # 设置窗口大小并居中显示
        self.center_detail_window(detail_window, window_width, window_height)
        detail_window.resizable(True, True)

        # 创建主框架
        main_frame = ttk.Frame(detail_window)
        main_frame.pack(fill='both', expand=True, padx=self.scale_value(5), pady=self.scale_value(5))
        
        # 创建滚动文本框
        text_widget = scrolledtext.ScrolledText(main_frame, wrap='word',
                                              padx=self.scale_value(10), pady=self.scale_value(10))
        text_widget.pack(fill='both', expand=True, pady=(0, self.scale_value(10)))
        text_widget.configure(font=('SimHei', self.scale_font_size(16)))
        
        # 显示详细信息
        # 合并症状和病情显示（避免重复内容）
        symptoms = case.get('symptoms', '').strip()
        condition = case.get('condition', '').strip()

        # 如果症状和病情内容相同，只显示一次
        if symptoms and condition and symptoms == condition:
            symptoms_condition_text = symptoms
        else:
            # 如果内容不同，分别显示
            symptoms_condition_parts = []
            if symptoms:
                symptoms_condition_parts.append(f"症状：{symptoms}")
            if condition and condition != symptoms:
                symptoms_condition_parts.append(f"病情：{condition}")
            symptoms_condition_text = '\n'.join(symptoms_condition_parts) if symptoms_condition_parts else '未知'

        details = f"""病例详细信息

姓名: {case.get('name', '未知')}
年龄: {case.get('age', '未知')}
性别: {case.get('gender', '未知')}
住址: {case.get('address', '未知')}
联系方式: {self.format_contact(case.get('contact', '未知'))}

症状:
{symptoms_condition_text}

诊断结果:
{case.get('diagnosis', '未知')}

处方:
{case.get('prescription', '未知')}

用法用量:
{case.get('usage', '未知')}

开方人: {case.get('prescriber', '未知')}

就诊时间: {self.format_visit_time(case.get('visit_time', '未知'))}

来源文件: {case.get('source_file', '未知')}
"""
        
        text_widget.insert('1.0', details)
        text_widget.config(state='disabled')

        # 添加选中文字复制功能
        self.setup_text_selection_copy(text_widget, detail_window)

        # 添加按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(5, 0))

        # 添加"复制信息"按钮
        copy_button = ttk.Button(button_frame, text="复制信息",
                                command=lambda: self.copy_case_to_add_form(case, detail_window))
        copy_button.pack(side='left', padx=(0, 10))

        # 添加"关闭"按钮
        close_button = ttk.Button(button_frame, text="关闭",
                                 command=detail_window.destroy)
        close_button.pack(side='right')
    
    def copy_case_to_add_form(self, case, detail_window):
        """将病例信息复制到添加新病例窗口"""
        try:
            # 存储复制的病例数据，用于右键粘贴功能
            self.copied_case_data = case.copy()
            # 复制基本信息到Entry控件
            self.add_vars['name_var'].set(case.get('name', ''))
            self.add_vars['age_var'].set(case.get('age', ''))
            self.add_vars['gender_var'].set(case.get('gender', ''))
            self.add_vars['address_var'].set(case.get('address', ''))
            self.add_vars['contact_var'].set(self.format_contact(case.get('contact', '')))
            # 处理就诊时间，解析并设置到年月日输入框
            visit_time = case.get('visit_time', '')
            if visit_time and visit_time != '未知':
                # 尝试解析时间格式
                visit_time_str = str(visit_time).strip()
                if '.' in visit_time_str:
                    # 处理 YYYY.M.D 格式
                    parts = visit_time_str.split('.')
                    if len(parts) >= 3:
                        self.year_var.set(parts[0])
                        self.month_var.set(parts[1])
                        self.day_var.set(parts[2])
                elif '-' in visit_time_str:
                    # 处理 YYYY-MM-DD 格式
                    date_part = visit_time_str.split(' ')[0]  # 去掉时间部分
                    parts = date_part.split('-')
                    if len(parts) >= 3:
                        self.year_var.set(parts[0])
                        self.month_var.set(str(int(parts[1])))  # 去掉前导零
                        self.day_var.set(str(int(parts[2])))    # 去掉前导零

            # 复制症状病情到Text控件（合并症状和病情，避免重复）
            self.symptoms_add_var_widget.delete('1.0', 'end')
            symptoms = case.get('symptoms', '').strip()
            condition = case.get('condition', '').strip()

            # 如果症状和病情内容相同，只显示一次
            if symptoms and condition and symptoms == condition:
                symptoms_condition_text = symptoms
            else:
                # 如果内容不同，分别显示
                symptoms_condition_parts = []
                if symptoms:
                    symptoms_condition_parts.append(f"症状：{symptoms}")
                if condition and condition != symptoms:
                    symptoms_condition_parts.append(f"病情：{condition}")
                symptoms_condition_text = '\n'.join(symptoms_condition_parts)

            if symptoms_condition_text:
                self.symptoms_add_var_widget.insert('1.0', symptoms_condition_text)

            # 复制诊断结果到Text控件
            self.diagnosis_var_widget.delete('1.0', 'end')
            diagnosis = case.get('diagnosis', '')
            if diagnosis:
                self.diagnosis_var_widget.insert('1.0', diagnosis)

            # 复制处方到Text控件
            self.prescription_var_widget.delete('1.0', 'end')
            prescription = case.get('prescription', '')
            if prescription:
                self.prescription_var_widget.insert('1.0', prescription)

            # 复制用法用量到Text控件
            self.usage_var_widget.delete('1.0', 'end')
            usage = case.get('usage', '')
            if usage:
                self.usage_var_widget.insert('1.0', usage)

            # 复制开方人
            prescriber = case.get('prescriber', '')
            if prescriber:
                self.add_vars['prescriber_var'].set(prescriber)

            # 显示成功消息 - 已注释掉弹窗
            # messagebox.showinfo("成功", "病例信息已复制到添加新病例窗口！")

            # 关闭详情窗口
            detail_window.destroy()

            # 将焦点设置到姓名输入框
            self.add_record_entries['name_var'].focus_set()

        except Exception as e:
            messagebox.showerror("错误", f"复制信息时出错: {str(e)}")
    
    def add_record(self):
        """添加新病例"""
        if not self.manager:
            messagebox.showwarning("警告", "请先加载Excel文件!")
            return
        
        # 获取输入数据
        name = self.add_vars['name_var'].get().strip()
        age = self.add_vars['age_var'].get().strip()
        gender = self.add_vars['gender_var'].get().strip()
        address = self.add_vars['address_var'].get().strip()
        contact = self.add_vars['contact_var'].get().strip()
        prescriber = self.add_vars['prescriber_var'].get().strip()  # 新增：获取开方人
        
        # 从Text widget获取多行文本
        symptoms = self.symptoms_add_var_widget.get('1.0', 'end-1c').strip()
        diagnosis = self.diagnosis_var_widget.get('1.0', 'end-1c').strip()
        prescription = self.prescription_var_widget.get('1.0', 'end-1c').strip()
        usage = self.usage_var_widget.get('1.0', 'end-1c').strip()

        # 获取用户输入的就诊时间（从年月日输入框）
        year = self.year_var.get().strip()
        month = self.month_var.get().strip()
        day = self.day_var.get().strip()

        # 构建就诊时间字符串
        if year and month and day:
            visit_time = f"{year}.{month}.{day}"
        else:
            # 如果用户没有完整输入就诊时间，使用当前日期（新格式）
            now = datetime.datetime.now()
            visit_time = f"{now.year}.{now.month}.{now.day}"

        # 验证必填字段
        if not all([name, symptoms, prescription]):
            messagebox.showwarning("警告", "请填写姓名、症状病情和处方!")
            return

        try:
            # 传递开方人参数
            success = self.manager.add_new_record(name, age, gender, address, contact, symptoms, diagnosis, prescription, usage, visit_time, prescriber)
            if success:
                messagebox.showinfo("成功", "新病例添加成功!")
                self.clear_add_form()
                self.update_status("新病例添加成功")
                
                # 更新文件信息
                record_count = len(self.manager.all_records)
                file_count = len([f for f in os.listdir(self.excel_folder_path)
                                if f.endswith(('.xlsx', '.xls', '.csv'))])
                self.file_info_var.set(f"已加载 {file_count} 个文件（Excel/CSV），共 {record_count} 条记录")
            else:
                messagebox.showerror("错误", "添加失败!")
        except Exception as e:
            messagebox.showerror("错误", f"添加病例时出错: {str(e)}")
    
    def clear_add_form(self):
        """清空添加表单"""
        for var in self.add_vars.values():
            if var:  # 检查var不为None（visit_time_var为None）
                var.set("")

        self.symptoms_add_var_widget.delete('1.0', 'end')
        self.diagnosis_var_widget.delete('1.0', 'end')
        self.prescription_var_widget.delete('1.0', 'end')
        self.usage_var_widget.delete('1.0', 'end')

        # 重置年月日输入框为当前日期
        now = datetime.datetime.now()
        self.year_var.set(str(now.year))
        self.month_var.set(str(now.month))
        self.day_var.set(str(now.day))
    
    def generate_test_data(self):
        """生成测试数据"""
        self.update_status("正在生成测试数据...")
        
        def generate_thread():
            try:
                self.create_test_data_internal()
                self.root.after(0, lambda: messagebox.showinfo("成功", "测试数据生成完成!"))
                self.root.after(0, lambda: self.update_status("测试数据生成完成"))
                
                # 自动设置文件夹路径
                if not self.excel_folder_path:
                    records_path = os.path.join(os.getcwd(), "records")
                    if os.path.exists(records_path):
                        self.excel_folder_path = records_path
                        self.root.after(0, lambda: self.folder_var.set(records_path))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"生成测试数据时出错: {str(e)}"))
                self.root.after(0, lambda: self.update_status("生成测试数据失败"))
        
        threading.Thread(target=generate_thread, daemon=True).start()
    
    def create_test_data_internal(self):
        """内部测试数据生成方法"""
        # 模拟病例数据
        test_data = [
            # 感冒相关病例
            {
                '姓名': '张三',
                '年龄': '28',
                '联系方式': '13800138001',
                '症状': '发热、咳嗽、流鼻涕、头痛',
                '病情': '普通感冒',
                '处方': '感冒灵颗粒 3次/日，布洛芬 发热时服用，多休息多喝水'
            },
            {
                '姓名': '李四',
                '年龄': '35',
                '联系方式': '13900139002',
                '症状': '咳嗽、发烧、鼻塞、喉咙痛',
                '病情': '上呼吸道感染',
                '处方': '阿莫西林 500mg 3次/日，复方甘草片 3次/日，多喝温水'
            },
            {
                '姓名': '王五',
                '年龄': '42',
                '联系方式': '13700137003',
                '症状': '流鼻涕、打喷嚏、轻微发热',
                '病情': '病毒性感冒',
                '处方': '维C银翘片 3次/日，扑热息痛 发热时服用，注意保暖'
            },
            
            # 胃病相关病例
            {
                '姓名': '赵六',
                '年龄': '45',
                '联系方式': '13600136004',
                '症状': '胃痛、恶心、腹胀、食欲不振',
                '病情': '慢性胃炎',
                '处方': '奥美拉唑 20mg 2次/日，胃复安 3次/日，饮食清淡规律'
            },
            {
                '姓名': '钱七',
                '年龄': '38',
                '联系方式': '13500135005',
                '症状': '上腹疼痛、烧心、反酸',
                '病情': '胃溃疡',
                '处方': '雷贝拉唑 20mg 2次/日，铝镁加混悬液 4次/日，避免辛辣食物'
            },
            {
                '姓名': '孙八',
                '年龄': '52',
                '联系方式': '13400134006',
                '症状': '胃部不适、饭后腹胀、嗳气',
                '病情': '功能性消化不良',
                '处方': '吗丁啉 3次/日，健胃消食片 3次/日，少食多餐'
            },
            
            # 高血压相关病例
            {
                '姓名': '周九',
                '年龄': '58',
                '联系方式': '13300133007',
                '症状': '头晕、头痛、心悸、疲劳',
                '病情': '高血压',
                '处方': '氨氯地平 5mg 1次/日，定期监测血压，低盐饮食'
            },
            {
                '姓名': '吴十',
                '年龄': '63',
                '联系方式': '13200132008',
                '症状': '头昏、胸闷、心慌',
                '病情': '高血压病',
                '处方': '依那普利 10mg 2次/日，阿司匹林 100mg 1次/日，适量运动'
            },
            
            # 糖尿病相关病例
            {
                '姓名': '郑十一',
                '年龄': '55',
                '联系方式': '13100131009',
                '症状': '多饮、多尿、多食、体重下降',
                '病情': '2型糖尿病',
                '处方': '二甲双胍 500mg 3次/日，定期监测血糖，控制饮食'
            },
            {
                '姓名': '陈十二',
                '年龄': '49',
                '联系方式': '13000130010',
                '症状': '口渴、尿频、视力模糊',
                '病情': '糖尿病',
                '处方': '格列齐特 80mg 2次/日，定期复查，运动疗法'
            }
        ]
        
        # 创建records文件夹
        os.makedirs("records", exist_ok=True)
        
        # 生成Excel文件
        df1 = pd.DataFrame(test_data[:6])
        df1.to_excel("records/内科病例.xlsx", index=False)
        
        df2 = pd.DataFrame(test_data[6:])
        df2.to_excel("records/专科病例.xlsx", index=False)
        
        # 生成不同格式的测试文件
        different_format_data = [
            {
                '患者姓名': '测试病人一',
                '年纪': '30',
                '电话号码': '13111131019',
                '主要症状': '头痛、发热、乏力',
                '疾病诊断': '病毒性感冒',
                '药物治疗': '对乙酰氨基酚 500mg 3次/日，多休息'
            },
            {
                '患者姓名': '测试病人二',
                '年纪': '25',
                '电话号码': '13000130020',
                '主要症状': '咳嗽、胸闷、低热',
                '疾病诊断': '支气管炎',
                '药物治疗': '阿奇霉素 250mg 1次/日，止咳糖浆 3次/日'
            }
        ]
        
        df3 = pd.DataFrame(different_format_data)
        df3.to_excel("records/格式测试.xlsx", index=False)
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(message)
    
    def run(self):
        """运行GUI应用"""
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap('icon.ico')  # 如果有图标文件
            pass
        except:
            pass
        
        # 启动主循环
        self.root.mainloop()


class MedicalRecordsManager:
    def __init__(self, excel_folder_path: str = "records"):
        """
        医疗记录管理器
        
        Args:
            excel_folder_path: 存放Excel文件的文件夹路径
        """
        self.excel_folder_path = excel_folder_path
        self.all_records = []
        self.column_mappings = {}
        self.extraction_errors = []  # 存储提取过程中的错误
        self.progress_callback = None  # 进度回调函数
        
        # 定义列名映射规则（保留原有格式支持）
        self.column_keywords = {
            'name': ['姓名', '患者', '病人', '名字', '患者姓名', '病人姓名'],
            'age': ['年龄', '岁', '年纪', '岁数'],
            'gender': ['性别', '男女', '性'],
            'address': ['住址', '地址', '家庭住址', '联系地址', '现住址'],
            'contact': ['电话', '手机', '联系方式', '联系电话', '手机号', '电话号码', '家长电话'],
            'symptoms': ['症状', '主诉', '表现', '症状表现', '临床表现', '主要症状'],
            'diagnosis': ['诊断结果', '诊断', '诊断', '疾病诊断', '临床诊断', '初步诊断', '确诊'],
            'condition': ['病情', '疾病', '病症'],
            'prescription': ['处方', '药方', '治疗', '用药', '治疗方案', '药物', '药物治疗'],
            'usage': ['用法用量', '用法', '用量', '服用方法', '服法', '煎服', '代煎'],
            'visit_time': ['就诊时间', '时间', '日期', '挂号时间', '诊疗时间'],
            'prescriber': ['开方人', '医生', '主治医生', '开方医生', '医师']  # 新增开方人关键词
        }
    
    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def call_progress_callback(self, current, total, message):
        """调用进度回调"""
        if self.progress_callback:
            return self.progress_callback(current, total, message)
        return True
    
    def load_excel_files_with_progress(self) -> bool:
        """带进度显示的Excel和CSV文件加载"""
        if not os.path.exists(self.excel_folder_path):
            return False

        # 扫描Excel和CSV文件
        excel_files = glob.glob(os.path.join(self.excel_folder_path, "*.xlsx")) + \
                     glob.glob(os.path.join(self.excel_folder_path, "*.xls"))
        csv_files = glob.glob(os.path.join(self.excel_folder_path, "*.csv"))

        all_files = excel_files + csv_files

        if not all_files:
            return False
        
        self.extraction_errors.clear()
        total_files = len(all_files)

        # 报告开始
        if not self.call_progress_callback(0, total_files, "开始加载文件..."):
            return False

        for i, file_path in enumerate(all_files):
            filename = os.path.basename(file_path)

            # 更新进度
            if not self.call_progress_callback(i, total_files, f"正在处理: {filename}"):
                return False  # 用户取消

            try:
                # 根据文件扩展名选择处理方式
                if file_path.lower().endswith('.csv'):
                    # 处理CSV文件
                    self.load_csv_file(file_path, filename)
                else:
                    # 处理Excel文件
                    # 首先尝试标准格式
                    if self.try_load_standard_format(file_path):
                        continue

                    # 然后尝试新的特殊格式（优化版本）
                    self.load_medical_card_format_optimized(file_path, filename)

            except Exception as e:
                error_msg = f"加载文件 {filename} 时出错: {str(e)}"
                self.extraction_errors.append(error_msg)
                continue
        
        # 报告完成
        self.call_progress_callback(total_files, total_files, "加载完成!")
        return len(self.all_records) > 0

    def load_csv_file(self, csv_file_path: str, filename: str):
        """加载单个CSV文件"""
        try:
            # 尝试不同的编码格式读取CSV文件
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            df = None

            for encoding in encodings:
                try:
                    df = pd.read_csv(csv_file_path, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue

            if df is None:
                error_msg = f"无法读取CSV文件 {filename}：编码格式不支持"
                self.extraction_errors.append(error_msg)
                return False

            if df.empty:
                error_msg = f"CSV文件 {filename} 为空"
                self.extraction_errors.append(error_msg)
                return False

            columns = df.columns.tolist()

            # 自动识别列映射
            mapping = self.find_column_mapping(columns)

            # 检查是否找到了基本的必需字段
            required_fields = ['name']
            found_fields = [field for field in required_fields if field in mapping]

            if len(found_fields) < len(required_fields):
                error_msg = f"CSV文件 {filename} 缺少必需的字段（姓名）"
                self.extraction_errors.append(error_msg)
                return False

            # 处理数据
            records_added = 0
            for _, row in df.iterrows():
                record = {}
                for field, col_name in mapping.items():
                    if col_name in df.columns:
                        value = row[col_name]
                        # 处理NaN值
                        if pd.isna(value):
                            record[field] = ""
                        else:
                            record[field] = str(value).strip()

                # 只添加有姓名的记录
                if record.get('name'):
                    record['source_file'] = filename
                    self.all_records.append(record)
                    records_added += 1

            if records_added == 0:
                error_msg = f"CSV文件 {filename} 中没有找到有效的病例记录"
                self.extraction_errors.append(error_msg)
                return False

            return True

        except Exception as e:
            error_msg = f"处理CSV文件 {filename} 时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            return False
    
    def load_medical_card_format_optimized(self, excel_file: str, filename: str):
        """优化的医疗卡片格式加载（并行处理多sheet）"""
        try:
            # 使用只读模式提高性能
            workbook = openpyxl.load_workbook(excel_file, read_only=True, data_only=True)
            sheet_names = workbook.sheetnames
            
            if not sheet_names:
                return
            
            # 更新进度
            self.call_progress_callback(0, len(sheet_names), f"{filename}: 准备处理 {len(sheet_names)} 个工作表")
            
            # 如果sheet数量较少，使用串行处理
            if len(sheet_names) <= 3:
                self.process_sheets_sequential(workbook, sheet_names, excel_file, filename)
            else:
                # 如果sheet数量较多，使用并行处理
                self.process_sheets_parallel(excel_file, sheet_names, filename)
            
            workbook.close()
            
        except Exception as e:
            error_msg = f"打开文件 {filename} 时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
    
    def process_sheets_sequential(self, workbook, sheet_names, excel_file, filename):
        """串行处理sheet（适用于sheet数量较少的情况）"""
        for i, sheet_name in enumerate(sheet_names):
            try:
                sheet = workbook[sheet_name]
                
                # 跳过空的sheet
                if sheet.max_row <= 1:
                    continue
                
                # 更新进度
                self.call_progress_callback(i, len(sheet_names), 
                                          f"{filename}: 处理工作表 '{sheet_name}'")
                
                patient_records = self.extract_patient_records_from_sheet(sheet, sheet_name, excel_file)
                
                if patient_records:
                    self.all_records.extend(patient_records)
                    
            except Exception as e:
                error_msg = f"处理文件 {filename} 的工作表 '{sheet_name}' 时出错: {str(e)}"
                self.extraction_errors.append(error_msg)
    
    def process_sheets_parallel(self, excel_file, sheet_names, filename):
        """并行处理sheet（适用于sheet数量较多的情况）"""
        def process_single_sheet(sheet_name):
            """处理单个sheet的函数"""
            try:
                # 在新线程中重新打开文件（openpyxl对象不能跨线程）
                workbook = openpyxl.load_workbook(excel_file, read_only=True, data_only=True)
                sheet = workbook[sheet_name]
                
                # 跳过空的sheet
                if sheet.max_row <= 1:
                    workbook.close()
                    return []
                
                records = self.extract_patient_records_from_sheet(sheet, sheet_name, excel_file)
                workbook.close()
                return records
                
            except Exception as e:
                error_msg = f"并行处理文件 {filename} 的工作表 '{sheet_name}' 时出错: {str(e)}"
                self.extraction_errors.append(error_msg)
                return []
        
        # 使用线程池并行处理
        max_workers = min(4, len(sheet_names))  # 限制线程数
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_sheet = {
                executor.submit(process_single_sheet, sheet_name): sheet_name 
                for sheet_name in sheet_names
            }
            
            # 收集结果
            completed_count = 0
            for future in concurrent.futures.as_completed(future_to_sheet):
                sheet_name = future_to_sheet[future]
                completed_count += 1
                
                # 更新进度
                self.call_progress_callback(completed_count, len(sheet_names), 
                                          f"{filename}: 已完成 {completed_count}/{len(sheet_names)} 个工作表")
                
                try:
                    records = future.result(timeout=60)  # 设置超时
                    if records:
                        self.all_records.extend(records)
                except Exception as e:
                    error_msg = f"获取工作表 '{sheet_name}' 结果时出错: {str(e)}"
                    self.extraction_errors.append(error_msg)
    
    def find_column_mapping(self, columns: List[str]) -> Dict[str, str]:
        """自动识别Excel列名对应的字段"""
        mapping = {}
        
        for field, keywords in self.column_keywords.items():
            best_match = ""
            best_score = 0
            
            for col in columns:
                for keyword in keywords:
                    # 检查是否包含关键词
                    if keyword in col:
                        score = len(keyword) / len(col)
                        if score > best_score:
                            best_score = score
                            best_match = col
                    
                    # 使用相似度匹配
                    similarity = SequenceMatcher(None, keyword, col).ratio()
                    if similarity > 0.6 and similarity > best_score:
                        best_score = similarity
                        best_match = col
            
            if best_match:
                mapping[field] = best_match
        
        return mapping
    
    def try_load_standard_format(self, excel_file: str) -> bool:
        """尝试加载标准表格格式的Excel文件"""
        try:
            # 读取Excel文件
            df = pd.read_excel(excel_file)
            
            if df.empty:
                return False
            
            columns = df.columns.tolist()
            
            # 自动识别列映射
            mapping = self.find_column_mapping(columns)
            
            # 如果没有找到足够的映射关系，说明不是标准格式
            # 至少需要姓名字段，症状或处方字段中的一个
            required_fields = ['name']
            optional_fields = ['symptoms', 'condition', 'prescription']

            found_required = [field for field in required_fields if field in mapping]
            found_optional = [field for field in optional_fields if field in mapping]

            if len(found_required) < len(required_fields) or len(found_optional) == 0:
                return False
            
            # 保存映射关系
            self.column_mappings[excel_file] = mapping
            
            # 处理数据
            for _, row in df.iterrows():
                record = {}
                for field, col_name in mapping.items():
                    if col_name in df.columns:
                        record[field] = str(row[col_name]) if pd.notna(row[col_name]) else ""
                
                if record:  # 只添加非空记录
                    record['source_file'] = os.path.basename(excel_file)
                    self.all_records.append(record)
            
            return True
            
        except Exception as e:
            return False
    
    def load_medical_card_format(self, excel_file: str):
        """加载医疗卡片格式的Excel文件（支持多sheet）"""
        try:
            # 使用openpyxl打开文件以获取所有sheet
            workbook = openpyxl.load_workbook(excel_file)
            
            for sheet_name in workbook.sheetnames:
                try:
                    sheet = workbook[sheet_name]
                    patient_records = self.extract_patient_records_from_sheet(sheet, sheet_name, excel_file)
                    
                    if patient_records:
                        self.all_records.extend(patient_records)
                    else:
                        # 记录没有找到数据的sheet
                        error_msg = f"文件 {os.path.basename(excel_file)} 的工作表 '{sheet_name}' 中未找到有效数据"
                        self.extraction_errors.append(error_msg)
                        
                except Exception as e:
                    error_msg = f"处理文件 {os.path.basename(excel_file)} 的工作表 '{sheet_name}' 时出错: {str(e)}"
                    self.extraction_errors.append(error_msg)
                    
        except Exception as e:
            error_msg = f"打开文件 {os.path.basename(excel_file)} 时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
    
    def extract_patient_records_from_sheet(self, sheet, sheet_name: str, excel_file: str) -> List[Dict]:
        """从工作表中提取患者记录"""
        records = []
        
        try:
            # 提取基本信息（第一行）
            basic_info = self.extract_basic_info(sheet)
            
            if not basic_info.get('name'):
                return []  # 如果没有姓名，跳过这个sheet
            
            # 查找所有就诊记录
            visit_records = self.extract_visit_records(sheet)
            
            # 为每个就诊记录创建完整的病例记录
            for visit in visit_records:
                record = basic_info.copy()
                record.update(visit)
                record['source_file'] = f"{os.path.basename(excel_file)}#{sheet_name}"
                records.append(record)
                
        except Exception as e:
            error_msg = f"提取工作表 '{sheet_name}' 数据时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return records
    
    def extract_basic_info(self, sheet) -> Dict:
        """提取基本信息（姓名、性别、年龄、住址、联系方式）"""
        basic_info = {}
        
        try:
            # 按照固定位置提取基本信息
            # A1: 姓名, B1: 性别, C1: 年龄, D1: 住址, E1: 联系方式
            basic_info['name'] = str(sheet.cell(1, 1).value or '').strip()
            basic_info['gender'] = str(sheet.cell(1, 2).value or '').strip()
            basic_info['age'] = str(sheet.cell(1, 3).value or '').strip()
            basic_info['address'] = str(sheet.cell(1, 4).value or '').strip()
            basic_info['contact'] = str(sheet.cell(1, 5).value or '').strip()
            
            # 如果住址列是空的，可能在其他位置
            if not basic_info['address']:
                # 尝试在其他可能的位置查找
                for col in range(1, 10):
                    cell_value = str(sheet.cell(1, col).value or '').strip()
                    if any(keyword in cell_value for keyword in ['路', '街', '区', '县', '市', '镇', '村', '号']):
                        basic_info['address'] = cell_value
                        break
            
        except Exception as e:
            error_msg = f"提取基本信息时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return basic_info
    
    def extract_visit_records(self, sheet) -> List[Dict]:
        """提取就诊记录"""
        visit_records = []
        
        try:
            max_row = sheet.max_row
            max_col = sheet.max_column
            
            # 扫描所有单元格，查找日期模式
            for row in range(2, max_row + 1):  # 从第2行开始，第1行是基本信息
                for col in range(1, max_col + 1):
                    cell = sheet.cell(row, col)
                    cell_value = str(cell.value or '').strip()
                    
                    # 检查是否是日期格式
                    if self.is_date_pattern(cell_value):
                        visit_record = self.extract_single_visit_record(sheet, row, col, cell_value)
                        if visit_record:
                            visit_records.append(visit_record)
            
        except Exception as e:
            error_msg = f"提取就诊记录时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return visit_records
    
    def is_date_pattern(self, text: str) -> bool:
        """检查文本是否符合日期模式"""
        if not text:
            return False
        
        # 检查 YYYY.MM.DD 或 YYYY.M.D 格式
        date_pattern = r'^\d{4}\.\d{1,2}\.\d{1,2}$'
        return bool(re.match(date_pattern, text))
    
    def extract_single_visit_record(self, sheet, date_row: int, date_col: int, visit_date: str) -> Dict:
        """提取单次就诊记录"""
        try:
            # 提取症状（日期同行的后续内容）
            symptoms = self.extract_symptoms_from_row(sheet, date_row, date_col)
            
            # 提取病情诊断（在症状下方查找）
            condition = self.extract_condition_below_symptoms(sheet, date_row, date_col)
            
            # 提取处方信息（在病情诊断下方查找）
            prescription_info = self.extract_prescription_info(sheet, date_row, date_col)
            
            # 构建就诊记录
            visit_record = {
                'visit_time': visit_date,
                'symptoms': symptoms,
                'condition': condition,
                'prescription': prescription_info.get('prescription', ''),
                'usage': prescription_info.get('usage', '')
            }
            
            # 只有包含有效信息的记录才返回
            if symptoms or condition or prescription_info.get('prescription'):
                return visit_record
            
        except Exception as e:
            error_msg = f"提取单次就诊记录时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return None
    
    def extract_symptoms_from_row(self, sheet, date_row: int, date_col: int) -> str:
        """从日期所在行提取症状信息"""
        symptoms_parts = []
        
        try:
            max_col = sheet.max_column
            
            # 从日期列的下一列开始提取症状
            for col in range(date_col + 1, max_col + 1):
                cell_value = str(sheet.cell(date_row, col).value or '').strip()
                if cell_value and not self.is_date_pattern(cell_value):
                    symptoms_parts.append(cell_value)
            
        except Exception as e:
            error_msg = f"提取症状时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return ' '.join(symptoms_parts)
    
    def extract_condition_below_symptoms(self, sheet, date_row: int, date_col: int) -> str:
        """提取病情诊断（通常在症状下方）"""
        try:
            max_col = sheet.max_column
            
            # 在日期下方的几行中查找病情诊断
            for row_offset in range(1, 5):  # 查找下方4行
                condition_row = date_row + row_offset
                
                for col in range(1, max_col + 1):
                    cell_value = str(sheet.cell(condition_row, col).value or '').strip()
                    
                    # 检查是否是病情诊断的模式
                    if self.looks_like_condition(cell_value):
                        return cell_value
            
        except Exception as e:
            error_msg = f"提取病情诊断时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return ''
    
    def looks_like_condition(self, text: str) -> bool:
        """判断文本是否像病情诊断"""
        if not text or len(text) < 2:
            return False
        
        # 常见的病情诊断关键词
        condition_keywords = ['不和', '虚', '虚弱', '炎', '病', '症', '证', '热', '寒', '湿', '燥', '瘀']
        
        # 检查是否包含病情关键词
        for keyword in condition_keywords:
            if keyword in text:
                return True
        
        # 检查是否是纯中文且长度适中（2-10个字符）
        if 2 <= len(text) <= 10 and all('\u4e00' <= char <= '\u9fff' for char in text):
            return True
            
        return False
    
    def extract_prescription_info(self, sheet, date_row: int, date_col: int) -> Dict:
        """提取处方信息"""
        prescription_parts = []
        usage_parts = []
        
        try:
            max_row = sheet.max_row
            max_col = sheet.max_column
            
            # 在日期下方查找处方信息
            for row in range(date_row + 1, max_row + 1):
                row_prescription = []
                found_usage = False
                
                for col in range(1, max_col + 1):
                    cell_value = str(sheet.cell(row, col).value or '').strip()
                    
                    if not cell_value:
                        continue
                    
                    # 检查是否是用法用量
                    if self.looks_like_usage(cell_value):
                        usage_parts.append(cell_value)
                        found_usage = True
                    # 检查是否是处方内容
                    elif self.looks_like_prescription(cell_value):
                        row_prescription.append(cell_value)
                    # 如果遇到新的日期，说明处方结束
                    elif self.is_date_pattern(cell_value):
                        break
                
                if row_prescription:
                    prescription_parts.extend(row_prescription)
                
                # 如果找到用法用量，通常处方就结束了
                if found_usage:
                    break
            
        except Exception as e:
            error_msg = f"提取处方信息时出错: {str(e)}"
            self.extraction_errors.append(error_msg)
            
        return {
            'prescription': ' '.join(prescription_parts),
            'usage': ' '.join(usage_parts)
        }
    
    def looks_like_usage(self, text: str) -> bool:
        """判断文本是否像用法用量"""
        if not text:
            return False
        
        usage_keywords = ['付', '代煎', '服用', '次', '日', '饭前', '饭后', '分下', '后下']
        
        for keyword in usage_keywords:
            if keyword in text:
                return True
                
        return False
    
    def looks_like_prescription(self, text: str) -> bool:
        """判断文本是否像处方内容"""
        if not text or len(text) < 2:
            return False
        
        # 如果包含数字，可能是药材+用量
        if re.search(r'\d+', text):
            return True
        
        # 常见中药材关键词
        herb_keywords = ['胡', '参', '芪', '归', '芍', '地', '苓', '术', '草', '花', '子', '仁', '皮', '根', '叶', '实']
        
        for keyword in herb_keywords:
            if keyword in text:
                return True
                
        return False
    
    def get_pinyin_initials(self, text: str) -> str:
        """获取文本的拼音首字母"""
        if not text:
            return ""

        # 获取拼音首字母
        initials = []
        for char in text:
            if '\u4e00' <= char <= '\u9fff':  # 判断是否为中文字符
                pinyin_list = lazy_pinyin(char, style=Style.FIRST_LETTER)
                if pinyin_list:
                    initials.append(pinyin_list[0].lower())
            else:
                # 非中文字符直接添加（如英文、数字等）
                initials.append(char.lower())

        return ''.join(initials)

    def fuzzy_match_pinyin(self, search_text: str, target_text: str) -> bool:
        """拼音首字母模糊匹配"""
        if not search_text or not target_text:
            return False

        search_text = search_text.lower().strip()
        target_text = target_text.strip()

        # 1. 直接包含匹配（原有功能）
        if search_text in target_text:
            return True

        # 2. 拼音首字母匹配
        target_initials = self.get_pinyin_initials(target_text)
        if search_text in target_initials:
            return True

        # 3. 拼音首字母开头匹配
        if target_initials.startswith(search_text):
            return True

        # 4. 分词后的拼音首字母匹配（处理多个词的情况）
        words = re.split(r'[，。、；：\s]+', target_text)
        for word in words:
            if word:
                word_initials = self.get_pinyin_initials(word)
                if search_text in word_initials or word_initials.startswith(search_text):
                    return True

        return False

    def calculate_similarity(self, symptoms1: str, condition1: str,
                           symptoms2: str, condition2: str) -> float:
        """计算两个病例的相似度"""
        # 症状相似度
        symptoms_sim = SequenceMatcher(None, symptoms1.lower(), symptoms2.lower()).ratio()

        # 病情相似度
        condition_sim = SequenceMatcher(None, condition1.lower(), condition2.lower()).ratio()

        # 综合相似度 (症状权重0.6，病情权重0.4)
        total_sim = symptoms_sim * 0.6 + condition_sim * 0.4

        return total_sim
    
    def search_similar_cases(self, name: str, gender: str, address: str, symptoms: str, condition: str, min_similarity: float = 0.3):
        similar_cases = []
        for record in self.all_records:
            # 基本信息匹配检查
            if not self.check_basic_info_match(record, name, gender, address):
                continue

            # 症状/病情相似度计算
            record_symptoms = record.get('symptoms', '')
            record_condition = record.get('condition', '')

            if symptoms or condition:
                similarity = self.calculate_similarity(
                    symptoms, condition, record_symptoms, record_condition
                )
                if similarity >= min_similarity:
                    similar_cases.append((record, similarity))
            else:
                # 如果只按基本信息查找，similarity设为1
                similar_cases.append((record, 1.0))

        # 按相似度排序（从高到低，最相关的在前面）
        similar_cases.sort(key=lambda x: x[1], reverse=True)

        # 如果相似度相同（比如只按姓名搜索），则按时间排序
        if similar_cases and all(case[1] == similar_cases[0][1] for case in similar_cases):
            # 按就诊时间排序，默认从近到远（倒序）
            similar_cases.sort(key=lambda x: self.parse_visit_time(x[0].get('visit_time', '')), reverse=True)

        return similar_cases  # 返回所有匹配的结果，不再限制数量

    def parse_visit_time(self, visit_time_str):
        """解析就诊时间字符串为可比较的日期对象"""
        try:
            # 处理格式如 "2024.1.3" 或 "2024.12.27"
            parts = visit_time_str.split('.')
            if len(parts) == 3:
                year = int(parts[0])
                month = int(parts[1])
                day = int(parts[2])
                return (year, month, day)
        except:
            pass
        return (0, 0, 0)  # 无法解析时返回最小值
    
    def check_basic_info_match(self, record: Dict, name: str, gender: str, address: str) -> bool:
        """检查基本信息是否匹配（支持拼音首字母模糊匹配）"""
        # 姓名匹配（支持拼音首字母匹配）
        if name:
            record_name = record.get('name', '')
            if not self.fuzzy_match_pinyin(name, record_name):
                return False

        # 性别匹配（支持拼音首字母匹配）
        if gender:
            record_gender = record.get('gender', '')
            if not self.fuzzy_match_pinyin(gender, record_gender):
                return False

        # 住址匹配（支持拼音首字母匹配）
        if address:
            record_address = record.get('address', '')
            if not self.fuzzy_match_pinyin(address, record_address):
                return False

        return True
    
    def add_new_record(self, name, age, gender, address, contact, symptoms, diagnosis, prescription, usage, visit_time, prescriber=''):
        new_record = {
            'name': name,
            'age': age,
            'gender': gender,
            'address': address,
            'contact': contact,
            'symptoms': symptoms,
            'condition': symptoms,  # 保持向后兼容，condition字段保留症状内容
            'diagnosis': diagnosis,
            'prescription': prescription,
            'usage': usage,
            'visit_time': visit_time,
            'prescriber': prescriber,  # 新增开方人字段
            'source_file': 'new_record'
        }
        self.all_records.append(new_record)
        self.save_new_record(new_record)
        return True
    
    def save_new_record(self, record: Dict):
        """保存新记录到Excel文件"""
        new_records_file = os.path.join(self.excel_folder_path, "new_records.xlsx")
        
        # 准备数据，添加开方人列
        record_data = {
            '姓名': [record.get('name', '')],
            '年龄': [record.get('age', '')],
            '性别': [record.get('gender', '')],
            '住址': [record.get('address', '')],
            '联系方式': [record.get('contact', '')],
            '症状': [record.get('symptoms', '')],
            '诊断结果': [record.get('diagnosis', '')],
            '病情': [record.get('condition', '')],
            '处方': [record.get('prescription', '')],
            '用法用量': [record.get('usage', '')],
            '开方人': [record.get('prescriber', '')],  # 新增开方人列
            '就诊时间': [record.get('visit_time', '')]
        }
        
        try:
            if os.path.exists(new_records_file):
                # 如果文件存在，追加数据
                existing_df = pd.read_excel(new_records_file)
                new_df = pd.DataFrame(record_data)
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            else:
                # 如果文件不存在，创建新文件
                combined_df = pd.DataFrame(record_data)
            
            combined_df.to_excel(new_records_file, index=False)
            
        except Exception as e:
            raise e

class SplashScreen:
    """启动画面"""
    def __init__(self):
        self.splash = tk.Tk()
        self.splash.title("医疗信息管理系统")

        # 注释掉分辨率计算缩放比例
        # screen_width = self.splash.winfo_screenwidth()
        # screen_height = self.splash.winfo_screenheight()
        # base_width = 1366
        # base_height = 768
        # width_scale = screen_width / base_width
        # height_scale = screen_height / base_height
        # self.scale_factor = min(width_scale, height_scale)
        # self.scale_factor = max(0.6, min(self.scale_factor, 2.0))

        # 使用固定窗口大小和位置
        screen_width = self.splash.winfo_screenwidth()
        screen_height = self.splash.winfo_screenheight()
        width = 600  # 固定宽度
        height = 400  # 固定高度
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        self.splash.geometry(f"{width}x{height}+{x}+{y}")

        # 设置固定缩放比例
        self.scale_factor = 1.0

        # 设置为无边框窗口
        self.splash.overrideredirect(True)
        self.splash.configure(bg='white')

        # 尝试加载背景图片 (暂时注释掉)
        self.background_image = None
        # self.load_background_image()

        self.create_widgets()

    def scale_value(self, value):
        """固定返回原始数值，不进行缩放"""
        return int(value)

    def scale_font_size(self, base_size):
        """固定返回原始字体大小，不进行缩放"""
        return base_size

    # 暂时注释掉背景图片加载功能
    # def load_background_image(self):
    #     """加载背景图片"""
    #     try:
    #         # 尝试加载背景图片，支持多种格式
    #         background_files = ['background.png', 'background.jpg', 'background.jpeg', 'bg.png', 'bg.jpg']
    #
    #         for bg_file in background_files:
    #             bg_path = get_resource_path(bg_file)
    #             if os.path.exists(bg_path):
    #                 # 使用PIL来处理图片（如果可用）
    #                 try:
    #                     from PIL import Image, ImageTk
    #                     # 打开图片并调整大小
    #                     image = Image.open(bg_path)
    #                     # 调整图片大小以适应窗口
    #                     image = image.resize((600, 400), Image.Resampling.LANCZOS)
    #                     self.background_image = ImageTk.PhotoImage(image)
    #                     return
    #                 except ImportError:
    #                     # 如果没有PIL，尝试直接使用PhotoImage（仅支持PNG/GIF）
    #                     if bg_file.lower().endswith('.png'):
    #                         self.background_image = tk.PhotoImage(file=bg_path)
    #                         return
    #
    #     except Exception as e:
    #         print(f"加载背景图片失败: {e}")
    #         self.background_image = None

    def create_widgets(self):
        """创建启动画面界面"""
        # 如果有背景图片，创建背景标签
        if self.background_image:
            # 创建背景标签
            bg_label = tk.Label(self.splash, image=self.background_image)
            bg_label.place(x=0, y=0, relwidth=1, relheight=1)

            # 主框架使用透明背景
            main_frame = tk.Frame(self.splash, bg='', padx=self.scale_value(30), pady=self.scale_value(30))
            main_frame.place(relx=0.5, rely=0.5, anchor='center')
        else:
            # 没有背景图片时使用白色背景
            main_frame = tk.Frame(self.splash, bg='white', padx=self.scale_value(30), pady=self.scale_value(30))
            main_frame.pack(fill='both', expand=True)

        # 根据是否有背景图片调整界面元素的背景色
        bg_color = '' if self.background_image else 'white'
        text_color = 'white' if self.background_image else '#2E8B57'

        # 系统图标区域
        icon_frame = tk.Frame(main_frame, bg=bg_color, height=self.scale_value(80))
        icon_frame.pack(fill='x', pady=(0, self.scale_value(20)))
        icon_frame.pack_propagate(False)

        # 尝试加载logo图片
        try:
            logo_files = ['logo.png', 'logo.jpg', 'icon.png', 'icon.jpg']
            logo_loaded = False
            for logo_file in logo_files:
                logo_path = get_resource_path(logo_file)
                if os.path.exists(logo_path):
                    try:
                        self.logo = tk.PhotoImage(file=logo_path)
                        logo_label = tk.Label(icon_frame, image=self.logo, bg=bg_color)
                        logo_label.pack()
                        logo_loaded = True
                        break
                    except:
                        continue

            if not logo_loaded:
                # 如果没有logo图片，显示文字图标
                icon_label = tk.Label(icon_frame, text="🏥", font=('SimHei', 36), bg=bg_color, fg=text_color)
                icon_label.pack()
        except:
            # 如果出错，显示文字图标
            icon_label = tk.Label(icon_frame, text="🏥", font=('SimHei', 36), bg=bg_color, fg=text_color)
            icon_label.pack()

        # 系统标题
        title_label = tk.Label(main_frame,
                              text="河南省漯河市弘济医院\n医疗诊疗信息管理系统",
                              font=('SimHei', 16, 'bold'),
                              bg=bg_color,
                              fg=text_color,
                              justify='center')
        title_label.pack(pady=(0, 20))

        # 版本信息
        version_color = 'lightgray' if self.background_image else '#666666'
        version_label = tk.Label(main_frame,
                                text="版本 1.0",
                                font=('SimHei', 10),
                                bg=bg_color,
                                fg=version_color)
        version_label.pack(pady=(0, 20))

        # 状态文本
        status_color = 'white' if self.background_image else '#333333'
        self.status_var = tk.StringVar(value="正在启动系统...")
        self.status_label = tk.Label(main_frame,
                                    textvariable=self.status_var,
                                    font=('SimHei', 12),
                                    bg=bg_color,
                                    fg=status_color)
        self.status_label.pack(pady=(0, 15))

        # 进度条
        self.progress = ttk.Progressbar(main_frame,
                                       mode='determinate',
                                       length=350,
                                       style='TProgressbar')
        self.progress.pack(pady=(0, 20))

        # 底部信息
        footer_color = 'lightgray' if self.background_image else '#999999'
        footer_label = tk.Label(main_frame,
                               text="© 2024 弘济医院. 保留所有权利.",
                               font=('SimHei', 9),
                               bg=bg_color,
                               fg=footer_color)
        footer_label.pack(side='bottom')
        
    def update_progress(self, value, status_text):
        """更新进度条和状态文本"""
        self.progress['value'] = value
        self.status_var.set(status_text)
        self.splash.update()
        
    def close(self):
        """关闭启动画面"""
        self.splash.destroy()

def show_startup_progress():
    """显示启动进度"""
    splash = SplashScreen()
    
    # 模拟启动过程
    startup_steps = [
        (10, "正在加载系统组件..."),
        (25, "正在初始化数据库连接..."),
        (40, "正在加载用户界面..."),
        (60, "正在检查系统权限..."),
        (80, "正在准备工作环境..."),
        (95, "启动完成..."),
        (100, "系统已就绪")
    ]
    
    import time
    for progress, status in startup_steps:
        splash.update_progress(progress, status)
        time.sleep(0.3)  # 模拟加载时间，你可以根据实际需要调整
    
    splash.close()
    return True

# 在import语句后添加这个函数
def get_resource_path(relative_path):
    """获取资源文件的正确路径，兼容开发环境和打包环境"""
    try:
        # PyInstaller 打包后的临时目录
        base_path = sys._MEIPASS
    except AttributeError:
        # 开发环境，使用当前脚本所在目录
        base_path = os.path.dirname(os.path.abspath(__file__))
    
    return os.path.join(base_path, relative_path)

if __name__ == "__main__":
    # 显示启动画面
    show_startup_progress()
    
    # 启动主程序
    app = MedicalRecordsGUI()
    app.run()